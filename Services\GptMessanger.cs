﻿
using Anthropic.SDK.Messaging;
using InterviewCopilot.Models;
using OpenAI.Chat;
using System.ClientModel;

namespace InterviewCopilot.Services
{
    public class GptMessanger : IChatMessanger
    {
        ChatClient client;
        DetailedInterview interview;

        public List<ChatMessage> conversation_history;
        string fileName = "";

        public GptMessanger(DetailedInterview _interview)
        {
            interview = _interview;
            var promptVariables = new Dictionary<string, string> {
                { "ProgrammingLanguage", interview.CandidateInterviewProfile.PrimaryTechnology }
            };

            fileName = $"{interview.Id}{DateTime.UtcNow:MM_dd_yyyy_hh_mm}.txt";
            conversation_history = [
                new SystemChatMessage(PromptFormatter.Format(PromptHub.InterviewerPrompt,promptVariables))
                //new SystemChatMessage($"Resume. {Configuration.Resume}")
                ];
        }
        public string Chat(string message)
        {
            client = new(model: interview.LlmModel.Name, interview.LlmModel.ApiKey);

            conversation_history.Add(new UserChatMessage(message));
            ChatCompletion completion = client.CompleteChat(conversation_history);
            var response = string.Join(@"\n", completion.Content.Select(x => x.Text));
            conversation_history.Add(new AssistantChatMessage(response));
            return response;
        }

        public async Task<string> ChatAsync(string message)
        {
            client = new(model: interview.LlmModel.Name, interview.LlmModel.ApiKey);

            conversation_history.Add(new UserChatMessage(message));
            ChatCompletion completion = await client.CompleteChatAsync(conversation_history);
            var response = string.Join(@"\n", completion.Content.Select(x => x.Text));
            conversation_history.Add(new AssistantChatMessage(response));
            SaveInterview();
            return response;
        }

        public async Task<AsyncCollectionResult<StreamingChatCompletionUpdate>> ChatStreamAsync(string message)
        {
            client = new(model: interview.LlmModel.Name, interview.LlmModel.ApiKey);

            conversation_history.Add(new UserChatMessage(message));
            ChatCompletion completion = await client.CompleteChatAsync(conversation_history);

            return client.CompleteChatStreamingAsync(conversation_history);
        }

        public async Task SaveInterview()
        {
            var filePath = @$"c:\code\interview\{fileName}";

            var conversession = conversation_history.Select(s =>
            {
                var userType = "System";
                if (s is SystemChatMessage)
                    userType = "System";
                else if (s is AssistantChatMessage)
                    userType = "Candidate";
                else if (s is UserChatMessage)
                    userType = "Interviewer";
                return $"{userType}: {string.Join("", s.Content.Select(s => s.Text))}";
            });
            var content = string.Join(Environment.NewLine, conversession);
        }

        public async Task SaveInterview(string message)
        {
            var filePath = @$"c:\code\interview\{fileName}";
            conversation_history.Add(new AssistantChatMessage(message));

            var conversession = conversation_history.Select(s =>
            {
                var userType = "System";
                if (s is SystemChatMessage)
                    userType = "System";
                else if (s is AssistantChatMessage)
                    userType = "Candidate";
                else if (s is UserChatMessage)
                    userType = "Interviewer";
                return $"{userType}: {string.Join("", s.Content.Select(s => s.Text))}";
            });
            var content = string.Join(Environment.NewLine, conversession);
        }


        public Task<IAsyncEnumerable<MessageResponse>> ChatAntStreamAsync(string message)
        {
            throw new NotImplementedException();
        }
    }
}
