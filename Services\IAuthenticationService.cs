using InterviewCopilot.Models;

namespace InterviewCopilot.Services;

public interface IAuthenticationService
{
    event EventHandler<bool>? AuthenticationStateChanged;
    
    bool IsAuthenticated { get; }
    UserInfo? CurrentUser { get; }
    string? Token { get; }
    
    Task<bool> LoginAsync(string email, string password);
    Task LogoutAsync();
    Task<bool> RefreshTokenAsync();
    string GetAuthorizationHeader();
}
