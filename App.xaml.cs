using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System.Windows;
using InterviewCopilot.Services;
using InterviewCopilot.ViewModels;
using InterviewCopilot.Views;
using Refit;
using System.Net.Http;

namespace InterviewCopilot;

public partial class App : Application
{
    private IHost? _host;

    protected override void OnStartup(StartupEventArgs e)
    {
        // Global exception handling
        AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
        DispatcherUnhandledException += OnDispatcherUnhandledException;
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Register HTTP client and Refit services
                services.AddHttpClient("InterviewCopilotApi", client =>
                {
                    client.BaseAddress = new Uri("https://unifiedhrm.com/api");
                    //client.DefaultRequestHeaders.Add("User-Agent", "InterviewCopilot/1.0");
                    client.Timeout = TimeSpan.FromSeconds(30);
                })
                .ConfigurePrimaryHttpMessageHandler(() =>
                {
                    var handler = new HttpClientHandler();
                    // For development with self-signed certificates
                    handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
                    return handler;
                });

                services.AddRefitClient<IAuthenticationApi>()
                    .ConfigureHttpClient(client =>
                    {
                        client.BaseAddress = new Uri("https://unifiedhrm.com/api");
                        client.Timeout = TimeSpan.FromSeconds(30);
                    })
                    .ConfigurePrimaryHttpMessageHandler(() =>
                    {
                        var handler = new HttpClientHandler();
                        handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
                        return handler;
                    });

                services.AddRefitClient<IInterviewApi>()
                    .ConfigureHttpClient(client =>
                    {
                        client.BaseAddress = new Uri("https://unifiedhrm.com/api");
                        client.Timeout = TimeSpan.FromSeconds(30);
                    })
                    .ConfigurePrimaryHttpMessageHandler(() =>
                    {
                        var handler = new HttpClientHandler();
                        handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
                        return handler;
                    });

                // Register services
                services.AddSingleton<IAuthenticationService, AuthenticationService>();
                services.AddSingleton<INavigationService, NavigationService>();
                services.AddTransient<IInterviewService, InterviewService>();
                services.AddSingleton<IValidationService, ValidationService>();
                services.AddSingleton<ISTTService, STTService>();
                services.AddSingleton<IAIAnswerService, AIAnswerService>();
                services.AddSingleton<IStreamingAIService, StreamingAIService>();

                // Register ViewModels
                services.AddTransient<LoginViewModel>();
                services.AddTransient<DashboardViewModel>();
                services.AddTransient<InterviewViewModel>();
                services.AddTransient<MainWindowViewModel>();

                // Register Views
                services.AddTransient<MainWindow>();
                services.AddTransient<LoginView>();
                services.AddTransient<DashboardView>();
                services.AddTransient<InterviewView>();
            })
            .Build();

        // Start the host
        _host.Start();

        var mainWindow = new MainWindow();
        mainWindow.SetServiceProvider(_host.Services);
        mainWindow.Show();

        base.OnStartup(e);
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _host?.Dispose();
        base.OnExit(e);
    }

    private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        var exception = e.ExceptionObject as Exception;
        MessageBox.Show($"An unexpected error occurred: {exception?.Message}",
                       "Error",
                       MessageBoxButton.OK,
                       MessageBoxImage.Error);
    }

    private void OnDispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
    {
        MessageBox.Show($"An unexpected error occurred: {e.Exception.Message}",
                       "Error",
                       MessageBoxButton.OK,
                       MessageBoxImage.Error);
        e.Handled = true;
    }
}
