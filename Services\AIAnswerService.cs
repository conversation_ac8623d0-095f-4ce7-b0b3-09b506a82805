using InterviewCopilot.Models;
using Microsoft.Extensions.Logging;
using System.Text;
using InterviewCopilot.AIModels;

namespace InterviewCopilot.Services;

public class AIAnswerService : IAIAnswerService
{
    private readonly ILogger<AIAnswerService> _logger;

    public bool IsConfigured => !string.IsNullOrEmpty(SpeechConfiguration.TextToSpeechAPIKey);

    public AIAnswerService(ILogger<AIAnswerService> logger)
    {
        _logger = logger;
    }

    public async Task<string> GenerateAnswerAsync(string question, DetailedInterview? interviewContext = null)
    {
        try
        {
            _logger.LogInformation("Generating AI answer for question: {Question}",
                                 question.Substring(0, Math.Min(50, question.Length)));

            if (interviewContext?.LlmModel == null)
            {
                _logger.LogWarning("No LLM model configuration found in interview context");
                return "Error: No AI model configured for this interview.";
            }

            // Determine provider based on LLM model
            var provider = DetermineProvider(interviewContext.LlmModel.Provider);
            _logger.LogInformation("Using {Provider} provider for AI response", provider);

            if (provider == "OpenAI")
            {
                var gptMessanger = new GptMessanger(interviewContext);
                return await gptMessanger.ChatAsync(question);
            }
            else if (provider == "Anthropic")
            {
                var anthropicMessanger = new AnthropicMessanger(interviewContext);
                return await anthropicMessanger.ChatAsync(question);
            }
            else
            {
                _logger.LogWarning("Unsupported provider: {Provider}, falling back to mock response", provider);
                // Fall back to mock response for unsupported providers
                var jobDescription = interviewContext?.JobDescription ?? "General interview";
                var candidateProfile = interviewContext?.CandidateInterviewProfile;
                return await GenerateAnswerAsync(question, jobDescription, candidateProfile);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating AI answer with interview context");
            throw;
        }
    }

    public async Task<string> GenerateAnswerAsync(string question, string jobDescription, CandidateInterviewProfile? candidateProfile = null)
    {
        try
        {
            _logger.LogDebug("Generating answer with job description and candidate profile");

            // Simulate processing time for realistic experience
            await Task.Delay(1500);

            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var answer = GenerateContextualAnswer(question, jobDescription, candidateProfile, timestamp);

            _logger.LogInformation("AI answer generated successfully");
            return answer;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating AI answer");
            throw;
        }
    }

    private string GenerateContextualAnswer(string question, string jobDescription, CandidateInterviewProfile? candidateProfile, string timestamp)
    {
        var answerBuilder = new StringBuilder();

        // Header
        answerBuilder.AppendLine("# 🤖 AI Generated Answer");
        answerBuilder.AppendLine($"*Generated at {timestamp}*");
        answerBuilder.AppendLine();

        // Question Analysis
        answerBuilder.AppendLine("## 📋 Question Analysis");
        answerBuilder.AppendLine($"**Question:** \"{question.Trim()}\"");
        answerBuilder.AppendLine();

        // Determine question type and provide specific guidance
        var questionType = AnalyzeQuestionType(question);
        answerBuilder.AppendLine($"**Question Type:** {questionType}");
        answerBuilder.AppendLine();

        // Context-aware response
        answerBuilder.AppendLine("## 💡 Suggested Response Strategy");
        
        if (candidateProfile != null)
        {
            answerBuilder.AppendLine("### 👤 Personalized Guidance");
            answerBuilder.AppendLine($"- **Primary Technology:** {candidateProfile.PrimaryTechnology}");
            answerBuilder.AppendLine($"- **Experience Level:** {candidateProfile.TotalYearsExperience} years");
            answerBuilder.AppendLine($"- **Background:** {candidateProfile.AboutYou}");
            answerBuilder.AppendLine();
        }

        // STAR Method Framework
        answerBuilder.AppendLine("### 🌟 STAR Method Framework");
        answerBuilder.AppendLine("Structure your response using the STAR method:");
        answerBuilder.AppendLine();
        answerBuilder.AppendLine("1. **Situation** - Set the context and background");
        answerBuilder.AppendLine("2. **Task** - Describe what needed to be accomplished");
        answerBuilder.AppendLine("3. **Action** - Explain the specific steps you took");
        answerBuilder.AppendLine("4. **Result** - Share the positive outcomes and impact");
        answerBuilder.AppendLine();

        // Question-specific guidance
        answerBuilder.AppendLine("### 🎯 Key Points to Address");
        var keyPoints = GetKeyPointsForQuestion(questionType, candidateProfile);
        foreach (var point in keyPoints)
        {
            answerBuilder.AppendLine($"- {point}");
        }
        answerBuilder.AppendLine();

        // Sample response structure
        answerBuilder.AppendLine("### 📝 Sample Response Structure");
        var sampleStructure = GetSampleStructure(questionType, candidateProfile);
        answerBuilder.AppendLine(sampleStructure);
        answerBuilder.AppendLine();

        // Additional tips
        answerBuilder.AppendLine("### 💡 Additional Tips");
        answerBuilder.AppendLine("- **Be Specific:** Use concrete examples and metrics when possible");
        answerBuilder.AppendLine("- **Show Growth:** Demonstrate learning and continuous improvement");
        answerBuilder.AppendLine("- **Stay Relevant:** Connect your answer to the role requirements");
        answerBuilder.AppendLine("- **Be Confident:** Speak with enthusiasm and conviction");
        answerBuilder.AppendLine("- **Keep it Concise:** Aim for 2-3 minutes maximum");
        answerBuilder.AppendLine();

        // Job relevance
        if (!string.IsNullOrEmpty(jobDescription) && jobDescription != "General interview")
        {
            answerBuilder.AppendLine("### 🎯 Job Relevance");
            answerBuilder.AppendLine("**Connect your answer to:**");
            answerBuilder.AppendLine($"- The specific requirements mentioned in: {jobDescription.Substring(0, Math.Min(100, jobDescription.Length))}...");
            answerBuilder.AppendLine("- How your experience aligns with the role");
            answerBuilder.AppendLine("- What value you can bring to the team");
            answerBuilder.AppendLine();
        }

        // Footer
        answerBuilder.AppendLine("---");
        answerBuilder.AppendLine("*This AI-generated guidance is designed to help you structure a compelling response. Adapt it to your personal experience and speaking style.*");

        return answerBuilder.ToString();
    }

    private string AnalyzeQuestionType(string question)
    {
        var lowerQuestion = question.ToLower();

        if (lowerQuestion.Contains("tell me about yourself") || lowerQuestion.Contains("introduce yourself"))
            return "Self-Introduction";
        
        if (lowerQuestion.Contains("weakness") || lowerQuestion.Contains("improve"))
            return "Weakness/Improvement";
        
        if (lowerQuestion.Contains("strength") || lowerQuestion.Contains("good at"))
            return "Strengths";
        
        if (lowerQuestion.Contains("experience") || lowerQuestion.Contains("worked on"))
            return "Experience-Based";
        
        if (lowerQuestion.Contains("challenge") || lowerQuestion.Contains("difficult") || lowerQuestion.Contains("problem"))
            return "Problem-Solving";
        
        if (lowerQuestion.Contains("team") || lowerQuestion.Contains("collaborate"))
            return "Teamwork";
        
        if (lowerQuestion.Contains("why") && (lowerQuestion.Contains("company") || lowerQuestion.Contains("role")))
            return "Motivation/Interest";
        
        if (lowerQuestion.Contains("where do you see") || lowerQuestion.Contains("future") || lowerQuestion.Contains("goals"))
            return "Career Goals";
        
        if (lowerQuestion.Contains("technical") || lowerQuestion.Contains("code") || lowerQuestion.Contains("algorithm"))
            return "Technical";

        return "General Behavioral";
    }

    private List<string> GetKeyPointsForQuestion(string questionType, CandidateInterviewProfile? profile)
    {
        var points = new List<string>();

        switch (questionType)
        {
            case "Self-Introduction":
                points.AddRange(new[]
                {
                    "Brief professional background and current role",
                    "Key skills and expertise areas",
                    "Notable achievements or projects",
                    "What motivates you professionally",
                    "Connection to the role you're applying for"
                });
                break;

            case "Weakness/Improvement":
                points.AddRange(new[]
                {
                    "Choose a real weakness that's not critical for the role",
                    "Show self-awareness and honesty",
                    "Demonstrate steps you're taking to improve",
                    "Provide specific examples of progress",
                    "Turn it into a growth opportunity"
                });
                break;

            case "Problem-Solving":
                points.AddRange(new[]
                {
                    "Clearly define the problem or challenge",
                    "Explain your analytical approach",
                    "Detail the solution you implemented",
                    "Quantify the results and impact",
                    "Lessons learned and future applications"
                });
                break;

            case "Technical":
                points.AddRange(new[]
                {
                    "Demonstrate deep technical knowledge",
                    "Use specific examples and technologies",
                    "Explain your thought process",
                    "Discuss trade-offs and alternatives",
                    "Show continuous learning mindset"
                });
                break;

            default:
                points.AddRange(new[]
                {
                    "Provide specific, concrete examples",
                    "Show impact and results",
                    "Demonstrate relevant skills",
                    "Connect to role requirements",
                    "Show growth and learning"
                });
                break;
        }

        return points;
    }

    private string GetSampleStructure(string questionType, CandidateInterviewProfile? profile)
    {
        var technology = profile?.PrimaryTechnology ?? "your technology stack";
        var experience = profile?.TotalYearsExperience ?? 3;

        return questionType switch
        {
            "Self-Introduction" => $"""
                "I'm a software developer with {experience} years of experience specializing in {technology}. 
                In my current role, I've [specific achievement]. I'm particularly passionate about [relevant area] 
                and have successfully [concrete example]. I'm excited about this opportunity because [connection to role]."
                """,

            "Problem-Solving" => $"""
                "In my previous role, we faced [specific challenge]. The impact was [business impact]. 
                I approached this by [analytical process]. I implemented [solution using {technology}]. 
                The result was [quantified outcome]. This experience taught me [lesson learned]."
                """,

            "Technical" => $"""
                "When working with {technology}, I focus on [technical approach]. For example, in [specific project], 
                I used [specific technologies/patterns] to [solve problem]. The key considerations were [trade-offs]. 
                This resulted in [technical and business outcomes]."
                """,

            _ => $"""
                "Let me share a specific example from my {experience} years of experience. [Situation context]. 
                My task was to [specific responsibility]. I took the following actions: [detailed steps]. 
                The outcome was [measurable results] which [business impact]."
                """
        };
    }

    private string DetermineProvider(string providerName)
    {
        return providerName?.ToLower() switch
        {
            "openai" => "OpenAI",
            "anthropic" => "Anthropic",
            _ => "Unknown"
        };
    }
}
