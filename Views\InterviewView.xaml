<UserControl x:Class="InterviewCopilot.Views.InterviewView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:markdig="clr-namespace:Markdig.Wpf;assembly=Markdig.Wpf"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <!--<Border Grid.Row="0" 
                Background="{StaticResource SurfaceBrush}" 
                Padding="24,16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                --><!-- Back Button --><!--
                <Button Grid.Column="0" 
                        Content="← Back" 
                        Background="Transparent"
                        BorderThickness="1"
                        BorderBrush="{StaticResource PrimaryBrush}"
                        Foreground="{StaticResource PrimaryBrush}"
                        Padding="12,6"
                        Command="{Binding GoBackCommand}"
                        VerticalAlignment="Center"/>

                --><!-- Interview Info --><!--
                <StackPanel Grid.Column="1" 
                           Margin="24,0"
                           VerticalAlignment="Center">
                    <TextBlock Text="{Binding InterviewTitle}" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="{StaticResource TextPrimaryBrush}"/>
                    <TextBlock Text="{Binding InterviewDetails}" 
                              FontSize="14" 
                              Foreground="{StaticResource TextSecondaryBrush}"/>
                </StackPanel>

                --><!-- End Interview Button --><!--
                <Button Grid.Column="2" 
                        Content="End Interview" 
                        Background="{StaticResource ErrorBrush}"
                        Foreground="White"
                        BorderThickness="0"
                        Padding="16,8"
                        Command="{Binding EndInterviewCommand}"
                        VerticalAlignment="Center"/>
            </Grid>
        </Border>-->

        <!-- Main Content Area -->
        <Grid Grid.Row="1" Margin="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="100"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Question Section with Fixed Height -->
            <Grid Grid.Row="0">

                <!-- Questions Panel with Fixed Height -->
                <Border Style="{StaticResource Card}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Questions Header -->
                        <Border Grid.Row="0"
                               Background="{StaticResource PrimaryBrush}"
                               CornerRadius="8,8,0,0"
                               Padding="4,2">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="Question (STT)"
                                          FontSize="16"
                                          FontWeight="Bold"
                                          Foreground="White"/>
                                <TextBlock Text="(Double-click to clear)"
                                          FontSize="12"
                                          Foreground="White"
                                          Opacity="0.8"
                                          Margin="8,0,0,0"
                                          VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- Questions Content -->
                        <ScrollViewer Grid.Row="1"
                                     VerticalScrollBarVisibility="Auto"
                                     Padding="0">
                            <TextBox x:Name="QuestionTextBox"
                                    Text="{Binding QuestionText, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                    TextWrapping="Wrap"
                                    AcceptsReturn="True"
                                    VerticalScrollBarVisibility="Auto"
                                    BorderThickness="1"
                                    BorderBrush="#E0E0E0"
                                    Background="White"
                                    FontSize="12"
                                    Height="100"
                                    Padding="0"
                                    MouseDoubleClick="QuestionTextBox_MouseDoubleClick"
                                    ToolTip="Auto-transcribed text from STT service. Double-click to clear."/>
                        </ScrollViewer>

                        <!-- STT Status -->
                        <!--<Border Grid.Row="2"
                               Background="#F5F5F5"
                               Padding="12,8"
                               CornerRadius="0,0,8,8">
                            <StackPanel Orientation="Horizontal">
                                <Ellipse Width="8" Height="8"
                                        Fill="{StaticResource SuccessBrush}"
                                        VerticalAlignment="Center"/>
                                <TextBlock Text="STT Active - Listening for speech..."
                                          FontSize="12"
                                          Foreground="{StaticResource TextSecondaryBrush}"
                                          Margin="8,0,0,0"
                                          VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>-->
                    </Grid>
                </Border>
            </Grid>

            <!-- Interview Conversation Panel with Maximum Space -->
            <Border Grid.Row="1" Style="{StaticResource Card}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Conversation Header -->
                        <Border Grid.Row="0"
                               Background="{StaticResource AccentBrush}"
                               CornerRadius="8,8,0,0"
                               Padding="4,2">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="Interview Conversation"
                                          FontSize="16"
                                          FontWeight="Bold"
                                          Foreground="White"/>
                                <Border Background="White"
                                       CornerRadius="10"
                                       Padding="2,1"
                                       Margin="4,0,0,0"
                                       Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <TextBlock Text="AI Responding..."
                                              FontSize="10"
                                              Foreground="{StaticResource AccentBrush}"
                                              FontWeight="Bold"/>
                                </Border>
                            </StackPanel>
                        </Border>

                        <!-- Conversation Content with Scrollable Markdown Viewer -->
                        <ScrollViewer Grid.Row="1"
                                     VerticalScrollBarVisibility="Auto"
                                     HorizontalScrollBarVisibility="Auto"
                                     CanContentScroll="True"
                                     PanningMode="VerticalOnly"
                                     Padding="0">
                            <markdig:MarkdownViewer Markdown="{Binding ConversationMarkdown}"
                                                   Background="Transparent"
                                                   BorderThickness="0"/>
                        </ScrollViewer>
                    </Grid>
                </Border>

            <!-- AI Answer Button at Bottom -->
            <StackPanel Grid.Row="2"
                       Orientation="Horizontal"
                       HorizontalAlignment="Center"
                       Margin="0,2,0,0">
                <Button Content="Get AI Answer"
                        Style="{StaticResource PrimaryButton}"
                        Command="{Binding GetAIAnswerCommand}"
                        IsEnabled="{Binding IsNotBusy}"
                        Padding="20,12"
                        FontSize="16"/>

                <StackPanel Orientation="Horizontal"
                           Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}"
                           Margin="4,0,0,0">
                    <ProgressBar IsIndeterminate="True"
                                Width="20"
                                Height="20"
                                VerticalAlignment="Center"/>
                    <TextBlock Text="Streaming AI answer..."
                              Margin="2,0,0,0"
                              VerticalAlignment="Center"
                              Foreground="{StaticResource TextSecondaryBrush}"/>
                    <TextBlock Text="(Real-time response)"
                              Margin="2,0,0,0"
                              VerticalAlignment="Center"
                              FontSize="10"
                              FontStyle="Italic"
                              Foreground="{StaticResource TextSecondaryBrush}"/>
                </StackPanel>
            </StackPanel>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" 
                Background="{StaticResource SurfaceBrush}" 
                Padding="24,12"
                BorderThickness="0,1,0,0"
                BorderBrush="#E0E0E0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Status Info -->
                <StackPanel Grid.Column="0"
                           Orientation="Horizontal"
                           VerticalAlignment="Center">
                    <StackPanel Orientation="Horizontal">
                        <Ellipse Width="8" Height="8" Fill="{StaticResource SuccessBrush}"/>
                        <TextBlock Text="Interview Active"
                                  FontSize="14"
                                  Foreground="{StaticResource TextSecondaryBrush}"
                                  Margin="4,0,0,0"/>
                    </StackPanel>

                    <TextBlock Text="This is a placeholder implementation"
                              FontSize="14"
                              Foreground="{StaticResource TextSecondaryBrush}"
                              FontStyle="Italic"
                              Margin="16,0,0,0"/>
                </StackPanel>

                <!-- Timer (Placeholder) -->
                <StackPanel Grid.Column="1"
                           Orientation="Horizontal"
                           VerticalAlignment="Center">
                    <TextBlock Text="⏱️" FontSize="16"/>
                    <TextBlock Text="00:00:00"
                              FontSize="16"
                              FontWeight="Bold"
                              Foreground="{StaticResource TextPrimaryBrush}"
                              Margin="8,0,0,0"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
