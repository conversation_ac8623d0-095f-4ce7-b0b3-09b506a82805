# Start Interview API Implementation

This document outlines the implementation of the start interview API call in the InterviewViewModel to retrieve detailed interview information when an interview session is initialized.

## API Endpoint Details

### Start Interview API
- **Endpoint**: `POST https://localhost:7243/api/consultant/interviews/{id}/start`
- **Headers**: 
  - `Content-Type: application/json`
  - `Authorization: Bearer Token`
- **Request Body**:
```json
{
    "Email": "<EMAIL>",
    "Password": "<PERSON><PERSON><PERSON>@PP2025"
}
```

## Implementation Overview

### 1. New Models Created

#### StartInterviewRequest
```csharp
public class StartInterviewRequest
{
    [JsonPropertyName("Email")]
    public string Email { get; set; }
    
    [JsonPropertyName("Password")]
    public string Password { get; set; }
}
```

#### StartInterviewResponse
```csharp
public class StartInterviewResponse
{
    [Json<PERSON>ropertyName("success")]
    public bool Success { get; set; }
    
    [Json<PERSON>ropertyName("message")]
    public string Message { get; set; }
    
    [J<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("interview")]
    public DetailedInterview Interview { get; set; }
}
```

#### DetailedInterview
Contains comprehensive interview information including:
- Basic interview details (ID, job description, dates, status)
- Session information (started date, started by)
- Interview type details
- Candidate interview profile
- LLM model configuration

#### CandidateInterviewProfile
```csharp
public class CandidateInterviewProfile
{
    public string Id { get; set; }
    public string AboutYou { get; set; }
    public string PrimaryTechnology { get; set; }
    public List<object> Experiences { get; set; }
    public List<object> Achievements { get; set; }
    public int TotalYearsExperience { get; set; }
    public DateTime ProfileCreatedAt { get; set; }
    public DateTime ProfileUpdatedAt { get; set; }
    public bool HasInterviewProfile { get; set; }
    public string CreatedBy { get; set; }
}
```

#### LlmModel
```csharp
public class LlmModel
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string DisplayName { get; set; }
    public string Provider { get; set; }
    public string Description { get; set; }
    public int ModelType { get; set; }
    public int MaxTokens { get; set; }
}
```

### 2. API Service Updates

#### IInterviewApi Interface
Added new method:
```csharp
[Post("/consultant/interviews/{id}/start")]
Task<StartInterviewResponse> StartInterviewAsync(
    [Header("Authorization")] string authorization,
    string id,
    [Body] StartInterviewRequest request);
```

#### InterviewService Implementation
Added `StartInterviewAsync` method with:
- Authentication validation
- Comprehensive error handling
- Detailed logging
- Response validation

### 3. Navigation System Enhancement

#### Updated Navigation Service
- Enhanced to support parameters in navigation
- Created `NavigationEventArgs` class for parameter passing
- Updated event handling to pass interview objects

#### MainWindowViewModel Updates
- Modified to handle parameterized navigation
- Properly initializes InterviewViewModel with interview data

### 4. InterviewViewModel Enhancements

#### New Properties
```csharp
public DetailedInterview? DetailedInterview { get; private set; }
```

#### Enhanced Initialize Method
```csharp
public async Task Initialize(Interview interview)
{
    CurrentInterview = interview;
    await StartInterviewSessionAsync(interview.Id);
    // Update UI with basic interview data
}
```

#### StartInterviewSessionAsync Method
- Calls the start interview API
- Stores detailed interview data in memory
- Logs comprehensive interview information
- Handles errors gracefully

## Data Flow

1. **User clicks "Start Interview"** on dashboard
2. **Navigation** to InterviewViewModel with Interview parameter
3. **Initialize method** called with interview data
4. **StartInterviewSessionAsync** called automatically
5. **API call** to start interview endpoint
6. **Detailed data** stored in `DetailedInterview` property
7. **Logging** of detailed information (not shown in UI)

## Information Stored in Memory

The following detailed information is retrieved and stored but **not displayed in UI**:

### Session Information
- Session started date/time (UTC)
- Session started by (user ID)
- Interview type and display name

### Candidate Profile
- About you description
- Primary technology
- Years of experience
- Experiences and achievements
- Profile creation/update dates

### LLM Model Configuration
- Model name and display name
- Provider (e.g., OpenAI)
- Model type and description
- Maximum tokens
- API configuration

### Enhanced Interview Details
- Complete job description
- Detailed notes
- Status information
- Timing details

## Security Considerations

### Credential Handling
- Uses same credentials as logged-in user
- Credentials should be retrieved from secure storage
- Current implementation uses hardcoded values for demo

### Data Privacy
- Detailed information kept in memory only
- No sensitive data displayed in UI
- Comprehensive logging for debugging

## Error Handling

### API Errors
- Network connectivity issues
- Authentication failures
- Invalid interview IDs
- Server errors

### User Experience
- Graceful error messages
- Fallback to basic interview data
- Comprehensive logging for troubleshooting

## Logging Implementation

### Debug Information Logged
```csharp
_logger.LogDebug("Interview session started at: {SessionStartedDate}", detailedInterview.SessionStartedDateUtc);
_logger.LogDebug("Interview type: {InterviewType}", detailedInterview.InterviewTypeDisplayName);
_logger.LogDebug("Candidate profile - Primary Technology: {Technology}, Experience: {Experience} years", 
               detailedInterview.CandidateInterviewProfile.PrimaryTechnology,
               detailedInterview.CandidateInterviewProfile.TotalYearsExperience);
_logger.LogDebug("LLM Model: {ModelName} ({Provider}), Max Tokens: {MaxTokens}", 
               detailedInterview.LlmModel.DisplayName,
               detailedInterview.LlmModel.Provider,
               detailedInterview.LlmModel.MaxTokens);
```

## Future Enhancements

### Potential Uses for Detailed Data
1. **AI Question Generation**: Use candidate profile and LLM model
2. **Personalized Interview Flow**: Adapt based on experience level
3. **Real-time Assistance**: Leverage LLM configuration
4. **Analytics**: Track interview patterns and outcomes
5. **Candidate Matching**: Use profile data for recommendations

### Security Improvements
1. **Secure Credential Storage**: Implement proper credential management
2. **Token Refresh**: Handle token expiration during interviews
3. **Data Encryption**: Encrypt sensitive data in memory
4. **Audit Logging**: Track access to detailed information

## Testing Checklist

- [ ] Start interview API call successful
- [ ] Detailed interview data retrieved and stored
- [ ] Navigation with parameters working
- [ ] Error handling for API failures
- [ ] Logging of detailed information
- [ ] UI shows basic interview data only
- [ ] Memory storage of sensitive information
- [ ] Graceful fallback on API errors
