﻿
using Anthropic.SDK;
using Anthropic.SDK.Constants;
using Anthropic.SDK.Messaging;
using InterviewCopilot.Models;
using InterviewCopilot.Services;
using OpenAI.Chat;
using System.ClientModel;

namespace InterviewCopilot.AIModels
{
    public class AnthropicMessanger : IChatMessanger
    {
        DetailedInterview interview;

        List<Message> conversation_history = new List<Message>();
        List<SystemMessage> systemMessage;
        string fileName = "";
        public AnthropicMessanger(DetailedInterview _interview)
        {
            interview = _interview;
            var promptVariables = new Dictionary<string, string> { 
                { "ProgrammingLanguage", interview.CandidateInterviewProfile.PrimaryTechnology }
            }; 
            fileName = $"{interview.Id}{DateTime.UtcNow:MM_dd_yyyy_hh_mm}.txt";
            systemMessage = new List<SystemMessage>()
                {
                    new SystemMessage (PromptFormatter.Format(PromptHub.InterviewerPrompt, promptVariables)),
                    //new SystemMessage ($"Resume. {Configuration.Resume}"),
                };
        }

        public async Task<string> ChatAsync(string message)
        {
            try
            {
                var client = new AnthropicClient(interview.LlmModel.ApiKey);
                var newMessage = new Message(RoleType.User, message);
                conversation_history.Add(newMessage);

                var parameters = new MessageParameters()
                {
                    System = systemMessage,
                    Messages = [newMessage],
                    MaxTokens = 1024,
                    Model = AnthropicModels.Claude35Sonnet,
                    Stream = false,
                    Temperature = .6m,
                    PromptCaching = PromptCacheType.Messages
                };

                var firstResult = await client.Messages.GetClaudeMessageAsync(parameters);
                var response = firstResult.Message.ToString();
                conversation_history.Add(new Message(RoleType.Assistant, response));
                return response;
            }
            catch (Exception ex)
            {
                return "Error.. Try Again";
            }
        }

        public async Task SaveInterview(string m)
        {
            var filePath = @$"c:\code\interview\{fileName}";
            conversation_history.Add(new Message(RoleType.Assistant, m));
            var conversession = conversation_history.Select(s =>
            {
                var userType = s.Role switch { RoleType.User => "Interviewer", RoleType.Assistant => "Interviewer" };

                return $"{userType}: {string.Join("", s.Content.SelectMany(s => (s as TextContent).Text))}";
            });
            var content = string.Join(Environment.NewLine, conversession);

        }


        public async Task<IAsyncEnumerable<MessageResponse>> ChatAntStreamAsync(string message)
        {
            var client = new AnthropicClient(interview.LlmModel.ApiKey);
            var newMessage = new Message(RoleType.User, message);
            conversation_history.Add(newMessage);

            var parameters = new MessageParameters()
            {
                System = systemMessage,
                Messages = conversation_history,
                MaxTokens = 1024,
                Model = AnthropicModels.Claude35Sonnet,
                Stream = false,
                Temperature = .6m,
                PromptCaching = PromptCacheType.Messages
            };

            return client.Messages.StreamClaudeMessageAsync(parameters);
        }

        public Task<AsyncCollectionResult<StreamingChatCompletionUpdate>> ChatStreamAsync(string message)
        {
            throw new NotImplementedException();
        }
    }
}
