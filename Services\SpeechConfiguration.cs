using Microsoft.Extensions.Configuration;

namespace InterviewCopilot.Services;

public static class SpeechConfiguration
{
    private static IConfiguration? _configuration;

    public static void Initialize(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    // Azure Speech Services Configuration
    public static string TextToSpeechAPIKey =>
        _configuration?["AzureSpeechServices:SubscriptionKey"] ?? "5bYGkbnN8L8O7uVzksREahMBO9kDMk6lg3iRML7FMJDBoD89aFbaJQQJ99BBACYeBjFXJ3w3AAAYACOGwP5L";

    public static string TextToSpeechRegion =>
        _configuration?["AzureSpeechServices:Region"] ?? "eastus";

    // Speech Recognition Settings
    public static string SpeechRecognitionLanguage =>
        _configuration?["AzureSpeechServices:Language"] ?? "en-US";

    public static int SampleRate =>
        _configuration?.GetValue<int>("SpeechRecognition:SampleRate") ?? 16000;

    public static int BitsPerSample =>
        _configuration?.GetValue<int>("SpeechRecognition:BitsPerSample") ?? 16;

    public static int Channels =>
        _configuration?.GetValue<int>("SpeechRecognition:Channels") ?? 1;

    // Audio Buffer Settings
    public static TimeSpan BufferDuration =>
        TimeSpan.FromSeconds(_configuration?.GetValue<int>("SpeechRecognition:BufferDurationSeconds") ?? 20);

    public static int ResamplerQuality =>
        _configuration?.GetValue<int>("SpeechRecognition:ResamplerQuality") ?? 60;

    public static int BufferSizeMs =>
        _configuration?.GetValue<int>("SpeechRecognition:BufferSizeMs") ?? 40;
}
