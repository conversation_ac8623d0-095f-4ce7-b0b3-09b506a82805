using InterviewCopilot.Models;
using Microsoft.Extensions.Logging;
using Refit;
using System.Net.Http;

namespace InterviewCopilot.Services;

public class InterviewService : IInterviewService
{
    private readonly IInterviewApi _interviewApi;
    private readonly IAuthenticationService _authService;
    private readonly ILogger<InterviewService> _logger;

    public InterviewService(
        IInterviewApi interviewApi, 
        IAuthenticationService authService,
        ILogger<InterviewService> logger)
    {
        _interviewApi = interviewApi;
        _authService = authService;
        _logger = logger;
    }

    public async Task<InterviewsResponse?> GetInterviewsAsync(int pageNumber = 1, int pageSize = 50, string? status = null)
    {
        try
        {
            if (!_authService.IsAuthenticated)
            {
                _logger.LogWarning("Attempted to get interviews without authentication");
                return null;
            }

            _logger.LogDebug("Making interviews API call - Page: {Page}, PageSize: {PageSize}", pageNumber, pageSize);
            _logger.LogDebug("Using authorization header: {AuthHeader}",
                           _authService.GetAuthorizationHeader().Substring(0, Math.Min(20, _authService.GetAuthorizationHeader().Length)) + "...");

            var response = await _interviewApi.GetInterviewsAsync(
                _authService.GetAuthorizationHeader(),
                pageNumber,
                pageSize);

            _logger.LogInformation("Successfully retrieved {Count} interviews from API", response.Interviews.Count);
            _logger.LogDebug("API Response - Success: {Success}, Message: {Message}, TotalCount: {TotalCount}",
                           response.Success, response.Message, response.TotalCount);

            return response;
        }
        catch (ApiException ex)
        {
            _logger.LogError(ex, "API error getting interviews - Status: {StatusCode}, Content: {Content}",
                           ex.StatusCode, ex.Content);
            return null;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Network error getting interviews: {Message}", ex.Message);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error getting interviews");
            return null;
        }
    }

    public async Task<Interview?> GetInterviewAsync(string id)
    {
        try
        {
            if (!_authService.IsAuthenticated)
            {
                _logger.LogWarning("Attempted to get interview without authentication");
                return null;
            }

            var interview = await _interviewApi.GetInterviewAsync(
                _authService.GetAuthorizationHeader(),
                id);

            _logger.LogInformation("Retrieved interview {Id}", id);
            return interview;
        }
        catch (ApiException ex)
        {
            _logger.LogError(ex, "API error getting interview {Id}", id);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error getting interview {Id}", id);
            return null;
        }
    }

    public async Task<bool> UpdateInterviewStatusAsync(string id, string status)
    {
        try
        {
            if (!_authService.IsAuthenticated)
            {
                _logger.LogWarning("Attempted to update interview status without authentication");
                return false;
            }

            await _interviewApi.UpdateInterviewStatusAsync(
                _authService.GetAuthorizationHeader(),
                id,
                new { status });

            _logger.LogInformation("Updated interview {Id} status to {Status}", id, status);
            return true;
        }
        catch (ApiException ex)
        {
            _logger.LogError(ex, "API error updating interview {Id} status", id);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error updating interview {Id} status", id);
            return false;
        }
    }

    public async Task<DetailedInterview?> StartInterviewAsync(string interviewId)
    {
        try
        {
            if (!_authService.IsAuthenticated)
            {
                _logger.LogWarning("Attempted to start interview without authentication");
                return null;
            }

          

            var response = await _interviewApi.StartInterviewAsync(
                _authService.GetAuthorizationHeader(),
                interviewId);

            if (response.Success && response.Interview != null)
            {
                _logger.LogInformation("Successfully started interview {InterviewId}. Status: {Status}, Type: {Type}",
                                     interviewId, response.Interview.StatusDisplayName, response.Interview.InterviewTypeDisplayName);

                _logger.LogDebug("Interview details - LLM Model: {LlmModel}, Candidate Profile: {HasProfile}",
                               response.Interview.LlmModel?.DisplayName, response.Interview.CandidateInterviewProfile?.HasInterviewProfile);

                return response.Interview;
            }
            else
            {
                _logger.LogWarning("Failed to start interview {InterviewId}: {Message}", interviewId, response.Message);
                return null;
            }
        }
        catch (ApiException ex)
        {
            _logger.LogError(ex, "API error starting interview {InterviewId} - Status: {StatusCode}, Content: {Content}",
                           interviewId, ex.StatusCode, ex.Content);
            return null;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Network error starting interview {InterviewId}: {Message}", interviewId, ex.Message);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error starting interview {InterviewId}", interviewId);
            return null;
        }
    }
}
