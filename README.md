# Interview Copilot - WPF Application

A modern WPF application built with .NET 8 for managing and conducting interviews. This application provides a clean, professional interface for consultants to view upcoming interviews and conduct interview sessions.

## Features

### 🔐 Authentication
- Secure login with email and password
- Bearer token-based authentication
- Session management with automatic logout

### 🏠 Dashboard
- Welcome screen with user information
- List of upcoming interviews
- Interview details including date, time, and job information
- One-click interview start functionality

### 📋 Interview Management
- Real-time interview interface (placeholder implementation)
- Question display area
- Markdown-based conversation viewer
- Interview timer and status tracking

### 🎨 Modern UI
- Material Design-inspired interface
- Responsive layout with proper data binding
- Loading indicators and error handling
- Professional color scheme and typography

## Technical Architecture

### Framework & Technologies
- **.NET 8** - Latest .NET framework
- **WPF** - Windows Presentation Foundation for rich desktop UI
- **MVVM Pattern** - Clean separation of concerns
- **Refit.NET** - Type-safe HTTP client for REST API communication
- **Microsoft.Toolkit.Mvvm** - MVVM helpers and commands
- **Markdig.Wpf** - Markdown rendering for conversation display

### Project Structure
```
InterviewCopilot/
├── Models/                 # Data models and DTOs
│   ├── LoginRequest.cs
│   ├── LoginResponse.cs
│   └── Interview.cs
├── Services/              # Business logic and API services
│   ├── IAuthenticationApi.cs
│   ├── IInterviewApi.cs
│   ├── AuthenticationService.cs
│   ├── InterviewService.cs
│   ├── NavigationService.cs
│   └── ValidationService.cs
├── ViewModels/            # MVVM ViewModels
│   ├── ViewModelBase.cs
│   ├── LoginViewModel.cs
│   ├── DashboardViewModel.cs
│   ├── InterviewViewModel.cs
│   └── MainWindowViewModel.cs
├── Views/                 # XAML Views and UI
│   ├── MainWindow.xaml
│   ├── LoginView.xaml
│   ├── DashboardView.xaml
│   └── InterviewView.xaml
├── Styles/               # UI Styles and themes
│   └── AppStyles.xaml
└── Converters/           # Value converters
    └── CountToVisibilityConverter.cs
```

## API Integration

### Authentication Endpoint
```
POST https://localhost:7243/api/consultant/login
Content-Type: application/json

{
    "Email": "<EMAIL>",
    "Password": "password"
}
```

### Interviews Endpoint
```
GET https://localhost:7243/api/consultant/interviews
Authorization: Bearer {token}
```

## Getting Started

### Prerequisites
- .NET 8 SDK
- Visual Studio 2022 or VS Code
- Windows 10/11

### Running the Application
1. Clone the repository
2. Navigate to the project directory
3. Restore dependencies: `dotnet restore`
4. Build the project: `dotnet build`
5. Run the application: `dotnet run`

### Configuration
- Update API base URL in `App.xaml.cs` if needed
- Ensure the backend API is running on `https://localhost:7243`

## Usage

1. **Login**: Enter your email and password on the login screen
2. **Dashboard**: View your upcoming interviews and click "Start Interview" when ready
3. **Interview**: Conduct interviews using the placeholder interface
4. **Navigation**: Use the top menu to navigate between sections

## Future Enhancements

The current implementation includes placeholder functionality for:
- Real-time interview questions generation
- Live conversation transcription
- AI-powered interview assistance
- Advanced interview analytics
- Multi-language support

## Security Features

- Secure token storage
- Input validation
- Error handling and logging
- Session timeout management

## Contributing

This is a demonstration project showcasing modern WPF development practices with clean architecture and proper separation of concerns.
