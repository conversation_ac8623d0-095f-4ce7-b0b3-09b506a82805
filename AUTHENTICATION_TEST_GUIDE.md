# Authentication Test Guide

This guide provides step-by-step instructions to test the authentication implementation against the backend REST API.

## Prerequisites

1. **Backend API Running**: Ensure the backend API is running on `https://localhost:7243`
2. **Test Credentials**: Use the provided test credentials:
   - Email: `<EMAIL>`
   - Password: `<PERSON><PERSON><PERSON>@PP2025`

## Test Scenarios

### 1. Successful Login Flow

**Steps:**
1. Launch the WPF application (`dotnet run`)
2. The login screen should appear with pre-filled credentials
3. Click the "Sign In" button
4. Verify the following sequence:

**Expected Results:**
- ✅ Login button becomes disabled during authentication
- ✅ Loading indicator appears
- ✅ Application navigates to Dashboard upon success
- ✅ Top menu bar appears with user information
- ✅ User's full name "<PERSON>pal Thakur" is displayed in the header
- ✅ Welcome message shows "Welcome back, Gopal Thakur!"

### 2. Token Storage and Usage

**Verification Points:**
- ✅ Bearer token is stored after successful login
- ✅ Token is included in subsequent API requests
- ✅ Token expiration is properly tracked
- ✅ Authentication state is maintained during session

### 3. Dashboard Functionality

**Expected Behavior:**
- ✅ Dashboard loads interview data using the stored token
- ✅ Interviews API call includes proper Authorization header
- ✅ Interview list displays correctly (if data available)
- ✅ "Start Interview" buttons are functional

### 4. Error Handling

**Test Cases:**

#### Invalid Credentials
1. Clear the password field and enter wrong password
2. Click "Sign In"
3. **Expected**: Error message "Invalid email or password. Please check your credentials and try again."

#### Network Connectivity Issues
1. Stop the backend API server
2. Attempt to login
3. **Expected**: Error message "Unable to connect to the server. Please check your internet connection and try again."

#### Malformed Email
1. Enter invalid email format (e.g., "invalid-email")
2. **Expected**: Sign In button should be disabled

### 5. Logout Functionality

**Steps:**
1. After successful login, click "Logout" button in top menu
2. **Expected**: 
   - ✅ User is redirected to login screen
   - ✅ Authentication token is cleared
   - ✅ User information is removed from header

## API Request/Response Verification

### Login Request Format
```
POST https://localhost:7243/api/consultant/login
Content-Type: application/json

{
    "Email": "<EMAIL>",
    "Password": "Shalini@PP2025"
}
```

### Expected Login Response
```json
{
    "success": true,
    "message": "Login successful",
    "token": "Bearer_Token_String_Here",
    "expiresAt": "2025-07-22T03:16:39.7579082Z",
    "user": {
        "id": "687e8e9ed832f10eb1112348",
        "email": "<EMAIL>",
        "firstName": "Gopal",
        "lastName": "Thakur",
        "fullName": "Gopal Thakur",
        "role": "CONSULTANT",
        "timeZone": "UTC"
    }
}
```

### Interviews API Request
```
GET https://localhost:7243/api/consultant/interviews?page=1&pageSize=50
Authorization: Bearer {token}
```

## Debugging Information

The application includes comprehensive logging. Check the console output for:

- Login attempt logs
- API request/response details
- Token storage confirmation
- Navigation events
- Error details

## Common Issues and Solutions

### SSL Certificate Issues
- The application is configured to accept self-signed certificates for localhost
- If you encounter SSL errors, ensure the backend API is using HTTPS

### CORS Issues
- Ensure the backend API allows requests from the WPF application
- Check that proper CORS headers are configured

### Token Format
- Verify the token is properly formatted in Authorization header as "Bearer {token}"
- Check that the token doesn't already include "Bearer " prefix

## Success Criteria Checklist

- [ ] Application starts without errors
- [ ] Login screen displays with pre-filled credentials
- [ ] Sign In button is clickable when valid credentials are entered
- [ ] Successful authentication with provided test credentials
- [ ] Navigation to dashboard after successful login
- [ ] User's full name "Gopal Thakur" displayed in application header
- [ ] Authentication token properly stored and used for API calls
- [ ] Appropriate error messages for failed login attempts
- [ ] Logout functionality works correctly
- [ ] Complete authentication flow: login → dashboard → interviews API

## Troubleshooting

If authentication fails:

1. **Check Backend API**: Ensure it's running on `https://localhost:7243`
2. **Verify Credentials**: Confirm the test credentials are correct
3. **Check Logs**: Review console output for detailed error information
4. **Network Issues**: Verify internet connectivity and firewall settings
5. **SSL Issues**: Ensure the backend API certificate is properly configured
