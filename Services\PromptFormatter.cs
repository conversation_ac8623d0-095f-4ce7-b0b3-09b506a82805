﻿using System.Text.RegularExpressions;

namespace InterviewCopilot.Services
{
    internal class PromptFormatter
    {
        /// <summary>
        /// Formats a string by replacing placeholders with values from a dictionary.
        /// Placeholders should be enclosed in curly braces, e.g., {PlaceholderName}.
        /// </summary>
        /// <param name="template">The string template containing placeholders.</param>
        /// <param name="replacements">A dictionary where keys are placeholder names (without braces) and values are their replacements.</param>
        /// <returns>The formatted string.</returns>
        public static string Format(string template, IDictionary<string, string> replacements)
        {
            // Handle null template or replacements gracefully
            if (string.IsNullOrEmpty(template))
            {
                Console.WriteLine("Warning: Template string is null or empty. Returning original template.");
                return template;
            }

            if (replacements == null || replacements.Count == 0)
            {
                Console.WriteLine("Warning: Replacements dictionary is null or empty. Returning original template.");
                return template;
            }

            string formattedString = template;

            // Iterate through each key-value pair in the replacements dictionary
            foreach (var entry in replacements)
            {
                // Construct the placeholder string to look for, e.g., "{ProgrammingLanguage}"
                string placeholder = $"{{{entry.Key}}}";

                // Replace all occurrences of the placeholder with its corresponding value
                // Using StringComparison.OrdinalIgnoreCase for case-insensitive replacement of the placeholder itself
                // Note: For simple string.Replace, it's case-sensitive by default for the placeholder.
                // If you need case-insensitive placeholder matching, a regex approach would be better.
                // For now, let's assume exact match of {PlaceholderName}.

                // A more robust way using Regex to handle multiple occurrences and ensure only full placeholders are replaced
                formattedString = Regex.Replace(formattedString, Regex.Escape(placeholder), entry.Value);
            }

            return formattedString;
        }
    }
}
