using Refit;
using InterviewCopilot.Models;

namespace InterviewCopilot.Services;

public interface IInterviewApi
{
    [Get("/consultant/interviews")]
    Task<InterviewsResponse> GetInterviewsAsync(
        [Header("Authorization")] string authorization,
        [Query] int page = 1,
        [Query] int pageSize = 50);

    [Get("/consultant/interviews/{id}")]
    Task<Interview> GetInterviewAsync(
        [Head<PERSON>("Authorization")] string authorization,
        string id);

    [Put("/consultant/interviews/{id}/status")]
    Task UpdateInterviewStatusAsync(
        [Header("Authorization")] string authorization,
        string id,
        [Body] object statusUpdate);

    [Post("/consultant/interviews/{id}/start")]
    Task<StartInterviewResponse> StartInterviewAsync(
        [Header("Authorization")] string authorization,
        string id);
}
