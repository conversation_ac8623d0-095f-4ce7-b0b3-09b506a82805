using Microsoft.Extensions.Logging;
using Microsoft.CognitiveServices.Speech;
using Microsoft.CognitiveServices.Speech.Audio;
using NAudio.Wave;
using NAudio.CoreAudioApi;
using NAudio.MediaFoundation;
using System.Windows;

namespace InterviewCopilot.Services;

public class STTService : ISTTService, IDisposable
{
    private readonly ILogger<STTService> _logger;
    private bool _isListening;
    private bool _disposed;

    // Azure Speech Services components
    private SpeechRecognizer? _recognizer;
    private PushAudioInputStream? _pushStream;

    // NAudio components for audio capture
    private WasapiLoopbackCapture? _capture;
    private BufferedWaveProvider? _bufferedWaveProvider;
    private MediaFoundationResampler? _resampler;
    private CancellationTokenSource? _cancellationTokenSource;

    public event EventHandler<string>? TextTranscribed;

    public bool IsListening => _isListening;

    public STTService(ILogger<STTService> logger)
    {
        _logger = logger;

        // Initialize MediaFoundation for audio processing
        MediaFoundationApi.Startup();
    }

    public async Task StartListeningAsync()
    {
        try
        {
            if (_isListening)
            {
                _logger.LogWarning("STT service is already listening");
                return;
            }

            _logger.LogInformation("Starting STT service with Azure Speech Services");

            await InitializeSpeechRecognizerAsync();

            _isListening = true;
            _logger.LogInformation("STT service started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting STT service");
            _isListening = false;
            await CleanupResourcesAsync();
            throw;
        }
    }

    public async Task StopListeningAsync()
    {
        try
        {
            if (!_isListening)
            {
                _logger.LogWarning("STT service is not currently listening");
                return;
            }

            _logger.LogInformation("Stopping STT service");

            _isListening = false;
            _cancellationTokenSource?.Cancel();

            await CleanupResourcesAsync();

            _logger.LogInformation("STT service stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping STT service");
        }
    }

    public void SimulateTranscription(string text)
    {
        if (_isListening)
        {
            _logger.LogDebug("Simulating transcription: {Text}", text);
            Application.Current?.Dispatcher.Invoke(() =>
            {
                TextTranscribed?.Invoke(this, text);
            });
        }
    }

    private async Task InitializeSpeechRecognizerAsync()
    {
        try
        {
            // Configure Azure Speech Services
            var config = SpeechConfig.FromSubscription(
                SpeechConfiguration.TextToSpeechAPIKey,
                SpeechConfiguration.TextToSpeechRegion);
            config.SpeechRecognitionLanguage = SpeechConfiguration.SpeechRecognitionLanguage;

            _logger.LogDebug("Azure Speech Config created for region: {Region}", SpeechConfiguration.TextToSpeechRegion);

            // Setup audio capture from system audio output (loopback)
            await SetupAudioCaptureAsync();

            // Create audio stream for Azure Speech Services
            var audioFormat = AudioStreamFormat.GetWaveFormatPCM(
                (uint)SpeechConfiguration.SampleRate,
                (byte)SpeechConfiguration.BitsPerSample,
                (byte)SpeechConfiguration.Channels);

            _pushStream = AudioInputStream.CreatePushStream(audioFormat) as PushAudioInputStream;
            var audioConfig = AudioConfig.FromStreamInput(_pushStream);

            // Create speech recognizer
            _recognizer = new SpeechRecognizer(config, audioConfig);

            // Setup event handlers
            _recognizer.Recognized += OnSpeechRecognized;
            _recognizer.Canceled += OnSpeechCanceled;
            _recognizer.SessionStarted += OnSessionStarted;
            _recognizer.SessionStopped += OnSessionStopped;

            // Start continuous recognition
            await _recognizer.StartContinuousRecognitionAsync();
            _logger.LogInformation("Azure Speech Recognition started");

            // Start audio capture
            _capture?.StartRecording();
            _logger.LogInformation("Audio capture started");

            // Wait for capture to be in capturing state
            while (_capture?.CaptureState == CaptureState.Starting)
            {
                await Task.Delay(10);
            }

            // Start audio processing task
            _cancellationTokenSource = new CancellationTokenSource();
            _ = Task.Run(() => ProcessAudioAsync(_cancellationTokenSource.Token));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing speech recognizer");
            throw;
        }
    }

    private async Task SetupAudioCaptureAsync()
    {
        try
        {
            // Get default audio output device for loopback capture
            var enumerator = new MMDeviceEnumerator();
            var device = enumerator.GetDefaultAudioEndpoint(DataFlow.Render, Role.Multimedia);

            _logger.LogDebug("Using audio device: {DeviceName}", device.FriendlyName);

            // Create loopback capture
            _capture = new WasapiLoopbackCapture(device);

            var inputFormat = _capture.WaveFormat;
            var desiredFormat = new WaveFormat(
                SpeechConfiguration.SampleRate,
                SpeechConfiguration.BitsPerSample,
                SpeechConfiguration.Channels);

            _logger.LogDebug("Input format: {InputFormat}, Desired format: {DesiredFormat}",
                           inputFormat, desiredFormat);

            // Create buffered wave provider
            _bufferedWaveProvider = new BufferedWaveProvider(inputFormat)
            {
                BufferDuration = SpeechConfiguration.BufferDuration,
                DiscardOnBufferOverflow = false
            };

            // Create resampler for format conversion
            _resampler = new MediaFoundationResampler(_bufferedWaveProvider, desiredFormat)
            {
                ResamplerQuality = SpeechConfiguration.ResamplerQuality
            };

            // Setup data available event
            _capture.DataAvailable += OnAudioDataAvailable;
            _capture.RecordingStopped += OnRecordingStopped;

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting up audio capture");
            throw;
        }
    }

    private void OnAudioDataAvailable(object? sender, WaveInEventArgs e)
    {
        try
        {
            _bufferedWaveProvider?.AddSamples(e.Buffer, 0, e.BytesRecorded);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing audio data");
        }
    }

    private void OnRecordingStopped(object? sender, StoppedEventArgs e)
    {
        _logger.LogInformation("Audio recording stopped");
        if (e.Exception != null)
        {
            _logger.LogError(e.Exception, "Audio recording stopped due to error");
        }
    }

    private async Task ProcessAudioAsync(CancellationToken cancellationToken)
    {
        try
        {
            var bufferSize = SpeechConfiguration.SampleRate * SpeechConfiguration.BitsPerSample / 8 *
                           SpeechConfiguration.Channels * SpeechConfiguration.BufferSizeMs / 1000;
            var buffer = new byte[bufferSize];

            _logger.LogDebug("Starting audio processing with buffer size: {BufferSize}", bufferSize);

            while (!cancellationToken.IsCancellationRequested &&
                   _capture?.CaptureState == CaptureState.Capturing)
            {
                try
                {
                    int bytesRead = _resampler?.Read(buffer, 0, buffer.Length) ?? 0;
                    if (bytesRead > 0)
                    {
                        _pushStream?.Write(buffer, bytesRead);
                    }

                    await Task.Delay(SpeechConfiguration.BufferSizeMs / 2, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in audio processing loop");
                    await Task.Delay(100, cancellationToken);
                }
            }

            _logger.LogDebug("Audio processing loop ended");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in audio processing task");
        }
    }

    private void OnSpeechRecognized(object? sender, SpeechRecognitionEventArgs e)
    {
        try
        {
            if (!string.IsNullOrEmpty(e.Result.Text))
            {
                _logger.LogDebug("Speech recognized: {Text}", e.Result.Text);

                Application.Current?.Dispatcher.Invoke(() =>
                {
                    TextTranscribed?.Invoke(this, e.Result.Text);
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling speech recognition result");
        }
    }

    private void OnSpeechCanceled(object? sender, SpeechRecognitionCanceledEventArgs e)
    {
        _logger.LogWarning("Speech recognition canceled: {Reason}, Error: {ErrorDetails}",
                          e.Reason, e.ErrorDetails);
    }

    private void OnSessionStarted(object? sender, SessionEventArgs e)
    {
        _logger.LogInformation("Speech recognition session started: {SessionId}", e.SessionId);
    }

    private void OnSessionStopped(object? sender, SessionEventArgs e)
    {
        _logger.LogInformation("Speech recognition session stopped: {SessionId}", e.SessionId);
    }

    private async Task CleanupResourcesAsync()
    {
        try
        {
            _cancellationTokenSource?.Cancel();

            if (_recognizer != null)
            {
                await _recognizer.StopContinuousRecognitionAsync();
                _recognizer.Dispose();
                _recognizer = null;
            }

            _capture?.StopRecording();
            _capture?.Dispose();
            _capture = null;

            _resampler?.Dispose();
            _resampler = null;

            _bufferedWaveProvider = null;

            _pushStream?.Dispose();
            _pushStream = null;

            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = null;

            _logger.LogDebug("STT resources cleaned up");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up STT resources");
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _ = Task.Run(async () => await CleanupResourcesAsync());
            MediaFoundationApi.Shutdown();
            _disposed = true;
        }
    }
}
