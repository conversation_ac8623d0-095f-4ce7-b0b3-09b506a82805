using Microsoft.Toolkit.Mvvm.Input;
using InterviewCopilot.Services;
using System.ComponentModel.DataAnnotations;
using Microsoft.Extensions.Logging;
using System.Net.Http;

namespace InterviewCopilot.ViewModels;

public class LoginViewModel : ViewModelBase
{
    private readonly IAuthenticationService _authService;
    private readonly INavigationService _navigationService;
    private readonly ILogger<LoginViewModel> _logger;

    private string _email = "<EMAIL>"; // Default for testing
    private string _password = "<PERSON><PERSON><PERSON>@PP2025"; // Default for testing

    public LoginViewModel(
        IAuthenticationService authService,
        INavigationService navigationService,
        ILogger<LoginViewModel> logger)
    {
        _authService = authService;
        _navigationService = navigationService;
        _logger = logger;

        LoginCommand = new AsyncRelayCommand(LoginAsync, CanLogin);
        
        // Listen for property changes to update command state
        PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(Email) || e.PropertyName == nameof(Password))
            {
                LoginCommand.NotifyCanExecuteChanged();
            }
        };
    }

    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Please enter a valid email address")]
    public string Email
    {
        get => _email;
        set
        {
            SetProperty(ref _email, value);
            ClearError();
        }
    }

    [Required(ErrorMessage = "Password is required")]
    [MinLength(6, ErrorMessage = "Password must be at least 6 characters")]
    public string Password
    {
        get => _password;
        set
        {
            SetProperty(ref _password, value);
            ClearError();
        }
    }

    public IAsyncRelayCommand LoginCommand { get; }

    private bool CanLogin()
    {
        var canLogin = !IsBusy &&
                      !string.IsNullOrWhiteSpace(Email) &&
                      !string.IsNullOrWhiteSpace(Password) &&
                      IsValidEmail(Email);

        _logger.LogDebug("CanLogin: {CanLogin}, IsBusy: {IsBusy}, Email: '{Email}', Password: '{HasPassword}', EmailValid: {EmailValid}",
                        canLogin, IsBusy, Email, !string.IsNullOrWhiteSpace(Password), IsValidEmail(Email));

        return canLogin;
    }

    private async Task LoginAsync()
    {
        try
        {
            IsBusy = true;
            ClearError();

            _logger.LogInformation("User attempting login with email: {Email}", Email);

            var success = await _authService.LoginAsync(Email, Password);

            if (success)
            {
                _logger.LogInformation("Login successful for {Email}, navigating to dashboard", Email);
                _navigationService.NavigateTo("Dashboard");
            }
            else
            {
                SetError("Invalid email or password. Please check your credentials and try again.");
                _logger.LogWarning("Login failed for {Email} - invalid credentials", Email);
            }
        }
        catch (HttpRequestException ex)
        {
            SetError("Unable to connect to the server. Please check your internet connection and try again.");
            _logger.LogError(ex, "Network error during login for {Email}", Email);
        }
        catch (TaskCanceledException ex)
        {
            SetError("Login request timed out. Please try again.");
            _logger.LogError(ex, "Timeout error during login for {Email}", Email);
        }
        catch (Exception ex)
        {
            SetError("An unexpected error occurred during login. Please try again.");
            _logger.LogError(ex, "Unexpected error during login for {Email}", Email);
        }
        finally
        {
            IsBusy = false;
        }
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    public void Initialize()
    {
        // Check if already authenticated
        if (_authService.IsAuthenticated)
        {
            _navigationService.NavigateTo("Dashboard");
        }
    }
}
