﻿
using Anthropic.SDK.Messaging;
using OpenAI.Chat;
using System.ClientModel;

namespace InterviewCopilot.Services
{
    public interface IChatMessanger
    {
        Task<string> ChatAsync(string message);
        Task<AsyncCollectionResult<StreamingChatCompletionUpdate>> ChatStreamAsync(string message);
        Task<IAsyncEnumerable<MessageResponse>> ChatAntStreamAsync(string message);
        Task SaveInterview(string message);
    }
}