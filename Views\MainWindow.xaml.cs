using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;
using InterviewCopilot.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace InterviewCopilot.Views;

public enum DisplayAffinity : uint
{
    WDA_NONE = 0x00000000,
    WDA_MONITOR = 0x00000001,
    WDA_EXCLUDEFROMCAPTURE = 0x00000011 // Prevents window content from being captured by screen capture APIs
}
public partial class MainWindow : Window
{
    [DllImport("user32.dll", SetLastError = true)]
    public static extern int SetWindowDisplayAffinity(IntPtr hWnd, DisplayAffinity dwAffinity);

    private IServiceProvider? _serviceProvider;

    // Parameterless constructor for XAML
    public MainWindow()
    {
        InitializeComponent();
        this.SourceInitialized += MainWindow_SourceInitialized;
        this.Closing += MainWindow_Closed;
    }

    // Constructor for dependency injection
    public MainWindow(IServiceProvider serviceProvider) : this()
    {
        _serviceProvider = serviceProvider;
        DataContext = _serviceProvider.GetRequiredService<MainWindowViewModel>();
    }

    // Method to set up dependency injection after construction
    public void SetServiceProvider(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        DataContext = _serviceProvider.GetRequiredService<MainWindowViewModel>();
    }

    protected void SetProtection(IntPtr hWnd, bool protect)
    {
        if (hWnd == IntPtr.Zero)
        {
            MessageBox.Show("Not ready for interview.");
            return;
        }

        DisplayAffinity affinity = protect ? DisplayAffinity.WDA_EXCLUDEFROMCAPTURE : DisplayAffinity.WDA_NONE;
        int result = SetWindowDisplayAffinity(hWnd, affinity);

        if (result != 0)
        {
            Console.WriteLine($"Window protection {(protect ? "enabled" : "disabled")} successfully.");
        }
        else
        {
            int error = Marshal.GetLastWin32Error();
            MessageBox.Show("Not ready for interview.");
        }
    }

    private void MainWindow_SourceInitialized(object? sender, EventArgs e)
    {
        // Get the window handle (HWND) after the window has been initialized
        IntPtr hWnd = new WindowInteropHelper(this).Handle;
        SetProtection(hWnd, true);
    }

    private void MainWindow_Closed(object? sender, EventArgs e)
    {
        // Revert the protection when the window closes (optional but good practice)
        IntPtr hWnd = new WindowInteropHelper(this).Handle;
        SetProtection(hWnd, false);
    }


    private void HomeButton_Click(object sender, RoutedEventArgs e)
    {
        if (DataContext is MainWindowViewModel viewModel && _serviceProvider != null)
        {
            // Navigate to dashboard
            var navigationService = _serviceProvider.GetRequiredService<Services.INavigationService>();
            navigationService.NavigateTo("Dashboard");
        }
    }

    private void UpdateMenuItem_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("Update functionality will be implemented in a future version.", 
                       "Update", 
                       MessageBoxButton.OK, 
                       MessageBoxImage.Information);
    }

    private void LogoutButton_Click(object sender, RoutedEventArgs e)
    {
        if (DataContext is MainWindowViewModel viewModel && _serviceProvider != null)
        {
            var authService = _serviceProvider.GetRequiredService<Services.IAuthenticationService>();
            _ = Task.Run(async () => await authService.LogoutAsync());
        }
    }

    private void ErrorOkButton_Click(object sender, RoutedEventArgs e)
    {
        if (DataContext is MainWindowViewModel viewModel)
        {
            viewModel.ClearError();
        }
    }
}
