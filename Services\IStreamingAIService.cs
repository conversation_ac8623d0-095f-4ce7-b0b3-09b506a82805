using InterviewCopilot.Models;

namespace InterviewCopilot.Services;

public interface IStreamingAIService
{
    IAsyncEnumerable<string> GetStreamingAnswerAsync(string question, DetailedInterview? interviewContext = null);
    IAsyncEnumerable<string> GetStreamingAnswerAsync(string question, string jobDescription, CandidateInterviewProfile? candidateProfile = null);
    bool IsConfigured { get; }
    LLMProvider Provider { get; }
}

public enum LLMProvider
{
    GPT,
    Anthropic,
    Local
}
