using Refit;
using InterviewCopilot.Models;

namespace InterviewCopilot.Services;

public interface IAuthenticationApi
{
    [Post("/consultant/login")]
    Task<LoginResponse> LoginAsync([Body] LoginRequest request);

    [Post("/consultant/logout")]
    Task LogoutAsync([Header("Authorization")] string authorization);

    [Get("/consultant/profile")]
    Task<UserInfo> GetProfileAsync([Header("Authorization")] string authorization);
}
