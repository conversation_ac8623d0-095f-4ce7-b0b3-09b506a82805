using InterviewCopilot.Models;

namespace InterviewCopilot.Services;

public interface IInterviewService
{
    Task<InterviewsResponse?> GetInterviewsAsync(int pageNumber = 1, int pageSize = 10, string? status = null);
    Task<Interview?> GetInterviewAsync(string id);
    Task<bool> UpdateInterviewStatusAsync(string id, string status);
    Task<DetailedInterview?> StartInterviewAsync(string interviewId);
}
