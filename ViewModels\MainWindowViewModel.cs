using InterviewCopilot.Models;
using InterviewCopilot.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace InterviewCopilot.ViewModels;

public class MainWindowViewModel : ViewModelBase
{
    private readonly INavigationService _navigationService;
    private readonly IAuthenticationService _authService;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MainWindowViewModel> _logger;

    private object? _currentViewModel;
    private string _currentViewName = "Login";

    public MainWindowViewModel(
        INavigationService navigationService,
        IAuthenticationService authService,
        IServiceProvider serviceProvider,
        ILogger<MainWindowViewModel> logger)
    {
        _navigationService = navigationService;
        _authService = authService;
        _serviceProvider = serviceProvider;
        _logger = logger;

        // Subscribe to navigation events
        _navigationService.NavigationRequested += OnNavigationRequested;
        _authService.AuthenticationStateChanged += OnAuthenticationStateChanged;

        // Initialize with login view
        NavigateToView("Login", null);
    }

    public object? CurrentViewModel
    {
        get => _currentViewModel;
        set => SetProperty(ref _currentViewModel, value);
    }

    public string CurrentViewName
    {
        get => _currentViewName;
        set => SetProperty(ref _currentViewName, value);
    }

    public bool IsAuthenticated => _authService.IsAuthenticated;

    public string UserName => _authService.CurrentUser?.Name ?? "User";

    private void OnNavigationRequested(object? sender, NavigationEventArgs e)
    {
        NavigateToView(e.ViewName, e.Parameter);
    }

    private void OnAuthenticationStateChanged(object? sender, bool isAuthenticated)
    {
        OnPropertyChanged(nameof(IsAuthenticated));
        OnPropertyChanged(nameof(UserName));

        if (!isAuthenticated)
        {
            NavigateToView("Login", null);
        }
    }

    private void NavigateToView(string viewName, object? parameter = null)
    {
        try
        {
            _logger.LogInformation("Navigating to view: {ViewName} with parameter: {HasParameter}", viewName, parameter != null);

            CurrentViewName = viewName;

            CurrentViewModel = viewName switch
            {
                "Login" => _serviceProvider.GetRequiredService<LoginViewModel>(),
                "Dashboard" => _serviceProvider.GetRequiredService<DashboardViewModel>(),
                "Interview" => _serviceProvider.GetRequiredService<InterviewViewModel>(),
                _ => throw new ArgumentException($"Unknown view: {viewName}")
            };

            // Initialize view models that need it
            if (CurrentViewModel is LoginViewModel loginVm)
            {
                loginVm.Initialize();
            }
            else if (CurrentViewModel is DashboardViewModel dashboardVm)
            {
                _ = Task.Run(async () => await dashboardVm.InitializeAsync());
            }
            else if (CurrentViewModel is InterviewViewModel interviewVm && parameter is Interview interview)
            {
                _ = Task.Run(async () => await interviewVm.Initialize(interview));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error navigating to view: {ViewName}", viewName);
            SetError($"Navigation error: {ex.Message}");
        }
    }
}
