using InterviewCopilot.Models;
using Microsoft.Extensions.Logging;
using System.Text;
using InterviewCopilot.AIModels;
using OpenAI.Chat;
using Anthropic.SDK.Messaging;
using System.ClientModel;

namespace InterviewCopilot.Services;

public class StreamingAIService : IStreamingAIService
{
    private readonly ILogger<StreamingAIService> _logger;

    public bool IsConfigured => true; // Always configured when interview context is available
    public LLMProvider Provider { get; private set; } = LLMProvider.Local;

    public StreamingAIService(ILogger<StreamingAIService> logger)
    {
        _logger = logger;
    }

    public async IAsyncEnumerable<string> GetStreamingAnswerAsync(string question, DetailedInterview? interviewContext = null)
    {
        _logger.LogInformation("Starting streaming AI answer for question: {Question}",
                             question.Substring(0, Math.Min(50, question.Length)));

        if (interviewContext?.LlmModel == null)
        {
            _logger.LogWarning("No LLM model configuration found in interview context");
            yield return "Error: No AI model configured for this interview.";
            yield break;
        }

        // Determine provider based on LLM model
        var provider = DetermineProvider(interviewContext.LlmModel.Provider);
        Provider = provider;

        _logger.LogInformation("Using {Provider} provider for AI response", provider);

        if (provider == LLMProvider.GPT)
        {
            await foreach (var chunk in GetGPTStreamingResponseAsync(question, interviewContext))
            {
                yield return chunk;
            }
        }
        else if (provider == LLMProvider.Anthropic)
        {
            await foreach (var chunk in GetAnthropicStreamingResponseAsync(question, interviewContext))
            {
                yield return chunk;
            }
        }
        else
        {
            _logger.LogWarning("Unsupported provider: {Provider}", provider);
            yield return "Error: Unsupported AI provider.";
        }
    }

    public async IAsyncEnumerable<string> GetStreamingAnswerAsync(string question, string jobDescription, CandidateInterviewProfile? candidateProfile = null)
    {
        _logger.LogDebug("Generating streaming answer with job description and candidate profile");

        // This method is used when no interview context is available
        // Fall back to mock response for now
        var fullResponse = GenerateContextualAnswer(question, jobDescription, candidateProfile);

        // Stream the response word by word to simulate real AI streaming
        var words = fullResponse.Split(' ');

        for (int i = 0; i < words.Length; i++)
        {
            // Simulate network delay and processing time
            await Task.Delay(Random.Shared.Next(50, 150));

            var word = words[i];
            if (i < words.Length - 1)
            {
                word += " ";
            }

            yield return word;
        }

        _logger.LogInformation("Streaming AI answer completed successfully");
    }

    private string GenerateContextualAnswer(string question, string jobDescription, CandidateInterviewProfile? candidateProfile)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss");
        var answerBuilder = new StringBuilder();

        // Header
        answerBuilder.AppendLine("# 🤖 AI Generated Answer");
        answerBuilder.AppendLine($"*Generated at {timestamp} via streaming*");
        answerBuilder.AppendLine();

        // Question Analysis
        answerBuilder.AppendLine("## 📋 Question Analysis");
        answerBuilder.AppendLine($"**Question:** \"{question.Trim()}\"");
        answerBuilder.AppendLine();

        // Determine question type and provide specific guidance
        var questionType = AnalyzeQuestionType(question);
        answerBuilder.AppendLine($"**Question Type:** {questionType}");
        answerBuilder.AppendLine();



        // Context-aware response
        answerBuilder.AppendLine("## 💡 Streaming Response Strategy");

        if (candidateProfile != null)
        {
            answerBuilder.AppendLine("### 👤 Personalized Guidance");
            answerBuilder.AppendLine($"- **Primary Technology:** {candidateProfile.PrimaryTechnology}");
            answerBuilder.AppendLine($"- **Experience Level:** {candidateProfile.TotalYearsExperience} years");
            answerBuilder.AppendLine($"- **Background:** {candidateProfile.AboutYou}");
            answerBuilder.AppendLine();
        }

        // STAR Method Framework
        answerBuilder.AppendLine("### 🌟 STAR Method Framework");
        answerBuilder.AppendLine("Structure your response using the STAR method:");
        answerBuilder.AppendLine();
        answerBuilder.AppendLine("1. **Situation** - Set the context and background");
        answerBuilder.AppendLine("2. **Task** - Describe what needed to be accomplished");
        answerBuilder.AppendLine("3. **Action** - Explain the specific steps you took");
        answerBuilder.AppendLine("4. **Result** - Share the positive outcomes and impact");
        answerBuilder.AppendLine();

        // Question-specific guidance
        answerBuilder.AppendLine("### 🎯 Key Points to Address");
        var keyPoints = GetKeyPointsForQuestion(questionType, candidateProfile);
        foreach (var point in keyPoints)
        {
            answerBuilder.AppendLine($"- {point}");
        }
        answerBuilder.AppendLine();

        // Sample response structure
        answerBuilder.AppendLine("### 📝 Sample Response Structure");
        var sampleStructure = GetSampleStructure(questionType, candidateProfile);
        answerBuilder.AppendLine(sampleStructure);
        answerBuilder.AppendLine();

        // Additional tips
        answerBuilder.AppendLine("### 💡 Additional Tips");
        answerBuilder.AppendLine("- **Be Specific:** Use concrete examples and metrics when possible");
        answerBuilder.AppendLine("- **Show Growth:** Demonstrate learning and continuous improvement");
        answerBuilder.AppendLine("- **Stay Relevant:** Connect your answer to the role requirements");
        answerBuilder.AppendLine("- **Be Confident:** Speak with enthusiasm and conviction");
        answerBuilder.AppendLine("- **Keep it Concise:** Aim for 2-3 minutes maximum");
        answerBuilder.AppendLine();

        // Job relevance
        if (!string.IsNullOrEmpty(jobDescription) && jobDescription != "General interview")
        {
            answerBuilder.AppendLine("### 🎯 Job Relevance");
            answerBuilder.AppendLine("**Connect your answer to:**");
            answerBuilder.AppendLine($"- The specific requirements mentioned in: {jobDescription.Substring(0, Math.Min(100, jobDescription.Length))}...");
            answerBuilder.AppendLine("- How your experience aligns with the role");
            answerBuilder.AppendLine("- What value you can bring to the team");
            answerBuilder.AppendLine();
        }

        // Footer
        answerBuilder.AppendLine("---");
        answerBuilder.AppendLine("*This AI-generated guidance was streamed in real-time to provide immediate assistance during your interview preparation.*");

        return answerBuilder.ToString();
    }

    private LLMProvider DetermineProvider(string providerName)
    {
        return providerName?.ToLower() switch
        {
            "openai" => LLMProvider.GPT,
            "anthropic" => LLMProvider.Anthropic,
            _ => LLMProvider.Local
        };
    }

    private async IAsyncEnumerable<string> GetGPTStreamingResponseAsync(string question, DetailedInterview interviewContext)
    {
        _logger.LogDebug("Starting GPT streaming response");

        var gptMessanger = new GptMessanger(interviewContext);
        var response = await gptMessanger.ChatStreamAsync(question);

        await foreach (StreamingChatCompletionUpdate update in response)
        {
            foreach (ChatMessageContentPart updatePart in update.ContentUpdate)
            {
                if (!string.IsNullOrEmpty(updatePart.Text))
                {
                    yield return updatePart.Text;
                }
            }
        }

        _logger.LogDebug("GPT streaming response completed");
    }

    private async IAsyncEnumerable<string> GetAnthropicStreamingResponseAsync(string question, DetailedInterview interviewContext)
    {
        _logger.LogDebug("Starting Anthropic streaming response");

        var anthropicMessanger = new AnthropicMessanger(interviewContext);
        var response = await anthropicMessanger.ChatAntStreamAsync(question);

        await foreach (var res in response)
        {
            if (res.Delta?.Text != null)
            {
                yield return res.Delta.Text;
            }
        }

        _logger.LogDebug("Anthropic streaming response completed");
    }

    private string AnalyzeQuestionType(string question)
    {
        var lowerQuestion = question.ToLower();

        if (lowerQuestion.Contains("tell me about yourself") || lowerQuestion.Contains("introduce yourself"))
            return "Self-Introduction";

        if (lowerQuestion.Contains("weakness") || lowerQuestion.Contains("improve"))
            return "Weakness/Improvement";

        if (lowerQuestion.Contains("strength") || lowerQuestion.Contains("good at"))
            return "Strengths";

        if (lowerQuestion.Contains("experience") || lowerQuestion.Contains("worked on"))
            return "Experience-Based";

        if (lowerQuestion.Contains("challenge") || lowerQuestion.Contains("difficult") || lowerQuestion.Contains("problem"))
            return "Problem-Solving";

        if (lowerQuestion.Contains("team") || lowerQuestion.Contains("collaborate"))
            return "Teamwork";

        if (lowerQuestion.Contains("why") && (lowerQuestion.Contains("company") || lowerQuestion.Contains("role")))
            return "Motivation/Interest";

        if (lowerQuestion.Contains("where do you see") || lowerQuestion.Contains("future") || lowerQuestion.Contains("goals"))
            return "Career Goals";

        if (lowerQuestion.Contains("technical") || lowerQuestion.Contains("code") || lowerQuestion.Contains("algorithm"))
            return "Technical";

        return "General Behavioral";
    }

    private List<string> GetKeyPointsForQuestion(string questionType, CandidateInterviewProfile? profile)
    {
        var points = new List<string>();

        switch (questionType)
        {
            case "Self-Introduction":
                points.AddRange(new[]
                {
                    "Brief professional background and current role",
                    "Key skills and expertise areas",
                    "Notable achievements or projects",
                    "What motivates you professionally",
                    "Connection to the role you're applying for"
                });
                break;

            case "Problem-Solving":
                points.AddRange(new[]
                {
                    "Clearly define the problem or challenge",
                    "Explain your analytical approach",
                    "Detail the solution you implemented",
                    "Quantify the results and impact",
                    "Lessons learned and future applications"
                });
                break;

            default:
                points.AddRange(new[]
                {
                    "Provide specific, concrete examples",
                    "Show impact and results",
                    "Demonstrate relevant skills",
                    "Connect to role requirements",
                    "Show growth and learning"
                });
                break;
        }

        return points;
    }

    private string GetSampleStructure(string questionType, CandidateInterviewProfile? profile)
    {
        var technology = profile?.PrimaryTechnology ?? "your technology stack";
        var experience = profile?.TotalYearsExperience ?? 3;

        return questionType switch
        {
            "Self-Introduction" => $"I'm a software developer with {experience} years of experience specializing in {technology}...",
            "Problem-Solving" => $"In my previous role, we faced [challenge]. I implemented [solution using {technology}]...",
            _ => $"Let me share a specific example from my {experience} years of experience..."
        };
    }
}
