{"format": 1, "restore": {"C:\\code\\interview_copilot\\InterviewCopilot.csproj": {}}, "projects": {"C:\\code\\interview_copilot\\InterviewCopilot.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\code\\interview_copilot\\InterviewCopilot.csproj", "projectName": "InterviewCop<PERSON>t", "projectPath": "C:\\code\\interview_copilot\\InterviewCopilot.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\code\\interview_copilot\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Anthropic.SDK": {"target": "Package", "version": "[4.7.1, )"}, "Markdig.Wpf": {"target": "Package", "version": "[*******, )"}, "Microsoft.CognitiveServices.Speech": {"target": "Package", "version": "[1.34.1, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Toolkit.Mvvm": {"target": "Package", "version": "[7.1.2, )"}, "NAudio": {"target": "Package", "version": "[2.2.1, )"}, "NAudio.Wasapi": {"target": "Package", "version": "[2.2.1, )"}, "OpenAI": {"target": "Package", "version": "[2.2.0, )"}, "Refit": {"target": "Package", "version": "[7.2.22, )"}, "Refit.HttpClientFactory": {"target": "Package", "version": "[7.2.22, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}}}