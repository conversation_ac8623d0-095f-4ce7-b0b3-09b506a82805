<UserControl x:Class="InterviewCopilot.Views.LoginView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">
    
    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="400"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Login Card -->
        <Border Grid.Row="1" Grid.Column="1" Style="{StaticResource Card}">
            <StackPanel>
                <!-- Header -->
                <StackPanel HorizontalAlignment="Center" Margin="0,0,0,20">
                    <TextBlock Text="Interview Copilot" 
                              FontSize="28" 
                              FontWeight="Bold" 
                              Foreground="{StaticResource PrimaryBrush}"
                              HorizontalAlignment="Center"/>
                    <TextBlock Text="Sign in to your account"
                              FontSize="16"
                              Foreground="{StaticResource TextSecondaryBrush}"
                              HorizontalAlignment="Center"
                              Margin="0,8,0,0"/>
                </StackPanel>

                <!-- Error Message -->
                <Border Background="{StaticResource ErrorBrush}"
                        CornerRadius="4"
                        Padding="12,8"
                        Margin="0,20,0,0"
                        Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="{Binding ErrorMessage}"
                              Foreground="White"
                              FontWeight="Medium"/>
                </Border>

                <!-- Email Field -->
                <StackPanel Margin="0,20,0,0">
                    <TextBlock Text="Email Address"
                              FontWeight="Medium"
                              Foreground="{StaticResource TextPrimaryBrush}"/>
                    <TextBox x:Name="EmailTextBox"
                            Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource ModernTextBox}"
                            IsEnabled="{Binding IsNotBusy}"
                            Margin="0,8,0,0"/>
                </StackPanel>

                <!-- Password Field -->
                <StackPanel Margin="0,20,0,0">
                    <TextBlock Text="Password"
                              FontWeight="Medium"
                              Foreground="{StaticResource TextPrimaryBrush}"/>
                    <PasswordBox x:Name="PasswordBox"
                                Style="{StaticResource ModernPasswordBox}"
                                IsEnabled="{Binding IsNotBusy}"
                                PasswordChanged="PasswordBox_PasswordChanged"
                                Margin="0,8,0,0"/>
                </StackPanel>

                <!-- Login Button -->
                <Button Content="Sign In"
                        Style="{StaticResource PrimaryButton}"
                        Command="{Binding LoginCommand}"
                        IsEnabled="{Binding IsNotBusy}"
                        Height="44"
                        FontSize="16"
                        Margin="0,20,0,0"/>

                <!-- Loading Indicator -->
                <StackPanel Orientation="Horizontal"
                           HorizontalAlignment="Center"
                           Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}"
                           Margin="0,20,0,0">
                    <ProgressBar IsIndeterminate="True"
                                Width="20"
                                Height="20"/>
                    <TextBlock Text="Signing in..."
                              Foreground="{StaticResource TextSecondaryBrush}"
                              Margin="8,0,0,0"/>
                </StackPanel>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
