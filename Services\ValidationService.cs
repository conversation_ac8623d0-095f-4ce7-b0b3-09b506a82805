using System.Text.RegularExpressions;

namespace InterviewCopilot.Services;

public class ValidationService : IValidationService
{
    private static readonly Regex EmailRegex = new(
        @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
        RegexOptions.Compiled | RegexOptions.IgnoreCase);

    public bool IsValidEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            return EmailRegex.IsMatch(email);
        }
        catch
        {
            return false;
        }
    }

    public bool IsValidPassword(string password)
    {
        return !string.IsNullOrWhiteSpace(password) && password.Length >= 6;
    }

    public string GetEmailValidationMessage(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return "Email address is required.";

        if (!IsValidEmail(email))
            return "Please enter a valid email address.";

        return string.Empty;
    }

    public string GetPasswordValidationMessage(string password)
    {
        if (string.IsNullOrWhiteSpace(password))
            return "Password is required.";

        if (password.Length < 6)
            return "Password must be at least 6 characters long.";

        return string.Empty;
    }
}
