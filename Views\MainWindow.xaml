<Window x:Class="InterviewCopilot.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:views="clr-namespace:InterviewCopilot.Views"
        xmlns:viewModels="clr-namespace:InterviewCopilot.ViewModels"
        mc:Ignorable="d"
        Title="Interview Copilot" 
        Height="800" 
        Width="1200"
        MinHeight="600"
        MinWidth="800"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <!-- Data Templates for ViewModels -->
        <DataTemplate DataType="{x:Type viewModels:LoginViewModel}">
            <views:LoginView/>
        </DataTemplate>

        <DataTemplate DataType="{x:Type viewModels:DashboardViewModel}">
            <views:DashboardView/>
        </DataTemplate>

        <DataTemplate DataType="{x:Type viewModels:InterviewViewModel}">
            <views:InterviewView/>
        </DataTemplate>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Top Menu Bar -->
        <Border Grid.Row="0" 
                Background="{StaticResource PrimaryBrush}" 
                Height="50"
                Visibility="{Binding IsAuthenticated, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Navigation Menu -->
                <StackPanel Grid.Column="0" 
                           Orientation="Horizontal" 
                           VerticalAlignment="Center"
                           Margin="20,0">
                    <Button Content="Home" 
                            Background="Transparent" 
                            Foreground="White" 
                            BorderThickness="0"
                            Padding="16,8"
                            Cursor="Hand"
                            Click="HomeButton_Click"/>
                    
                    <Menu Background="Transparent" 
                          VerticalAlignment="Center">
                        <MenuItem Header="Help" 
                                 Foreground="White" 
                                 Background="Transparent"
                                 Padding="16,8">
                            <MenuItem Header="Update" 
                                     Click="UpdateMenuItem_Click"/>
                        </MenuItem>
                    </Menu>
                </StackPanel>

                <!-- User Info -->
                <StackPanel Grid.Column="1" 
                           Orientation="Horizontal" 
                           VerticalAlignment="Center"
                           Margin="20,0">
                    <TextBlock Text="{Binding UserName}" 
                              Foreground="White" 
                              VerticalAlignment="Center"
                              Margin="0,0,16,0"/>
                    <Button Content="Logout" 
                            Background="Transparent" 
                            Foreground="White" 
                            BorderThickness="1"
                            BorderBrush="White"
                            Padding="12,6"
                            Cursor="Hand"
                            Click="LogoutButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content Area -->
        <ContentControl Grid.Row="1"
                       Content="{Binding CurrentViewModel}"/>

        <!-- Error Overlay -->
        <Border Grid.RowSpan="2"
                Background="#80000000"
                Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Border Background="White" 
                    CornerRadius="8" 
                    Padding="20"
                    MaxWidth="400"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center">
                <StackPanel>
                    <TextBlock Text="Error"
                              FontSize="18"
                              FontWeight="Bold"
                              Foreground="{StaticResource ErrorBrush}"/>
                    <TextBlock Text="{Binding ErrorMessage}"
                              TextWrapping="Wrap"
                              Margin="0,16,0,0"/>
                    <Button Content="OK"
                            Style="{StaticResource PrimaryButton}"
                            HorizontalAlignment="Right"
                            Click="ErrorOkButton_Click"
                            Margin="0,16,0,0"/>
                </StackPanel>
            </Border>
        </Border>
    </Grid>
</Window>
