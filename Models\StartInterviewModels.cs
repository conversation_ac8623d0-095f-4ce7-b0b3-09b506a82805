using System.Text.Json.Serialization;

namespace InterviewCopilot.Models;

public class StartInterviewRequest
{
    [JsonPropertyName("Email")]
    public string Email { get; set; } = string.Empty;

    [JsonPropertyName("Password")]
    public string Password { get; set; } = string.Empty;
}

public class StartInterviewResponse
{
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("interview")]
    public DetailedInterview? Interview { get; set; }
}

public class DetailedInterview
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("jobDescription")]
    public string JobDescription { get; set; } = string.Empty;

    [JsonPropertyName("interviewDateTimeUtc")]
    public DateTime InterviewDateTimeUtc { get; set; }

    [JsonPropertyName("interviewDateTimeLocal")]
    public DateTime InterviewDateTimeLocal { get; set; }

    [JsonPropertyName("timeZone")]
    public string TimeZone { get; set; } = string.Empty;

    [JsonPropertyName("status")]
    public int Status { get; set; }

    [JsonPropertyName("statusDisplayName")]
    public string StatusDisplayName { get; set; } = string.Empty;

    [JsonPropertyName("notes")]
    public string Notes { get; set; } = string.Empty;

    [JsonPropertyName("createdDateUtc")]
    public DateTime CreatedDateUtc { get; set; }

    [JsonPropertyName("sessionStartedDateUtc")]
    public DateTime? SessionStartedDateUtc { get; set; }

    [JsonPropertyName("sessionStartedBy")]
    public string? SessionStartedBy { get; set; }

    [JsonPropertyName("interviewType")]
    public int InterviewType { get; set; }

    [JsonPropertyName("interviewTypeDisplayName")]
    public string InterviewTypeDisplayName { get; set; } = string.Empty;

    [JsonPropertyName("candidate")]
    public object? Candidate { get; set; } // Can be null

    [JsonPropertyName("candidateInterviewProfile")]
    public CandidateInterviewProfile? CandidateInterviewProfile { get; set; }

    [JsonPropertyName("llmModel")]
    public LlmModel? LlmModel { get; set; }
}

public class CandidateInterviewProfile
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("aboutYou")]
    public string AboutYou { get; set; } = string.Empty;

    [JsonPropertyName("primaryTechnology")]
    public string PrimaryTechnology { get; set; } = string.Empty;

    [JsonPropertyName("experiences")]
    public List<object> Experiences { get; set; } = new();

    [JsonPropertyName("achievements")]
    public List<object> Achievements { get; set; } = new();

    [JsonPropertyName("totalYearsExperience")]
    public int TotalYearsExperience { get; set; }

    [JsonPropertyName("profileCreatedAt")]
    public DateTime ProfileCreatedAt { get; set; }

    [JsonPropertyName("profileUpdatedAt")]
    public DateTime ProfileUpdatedAt { get; set; }

    [JsonPropertyName("hasInterviewProfile")]
    public bool HasInterviewProfile { get; set; }

    [JsonPropertyName("createdBy")]
    public string CreatedBy { get; set; } = string.Empty;
}

public class LlmModel
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("apiKey")]
    public string ApiKey { get; set; } = string.Empty;

    [JsonPropertyName("displayName")]
    public string DisplayName { get; set; } = string.Empty;

    [JsonPropertyName("provider")]
    public string Provider { get; set; } = string.Empty;

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    [JsonPropertyName("modelType")]
    public int ModelType { get; set; }

    [JsonPropertyName("maxTokens")]
    public int MaxTokens { get; set; }
}
