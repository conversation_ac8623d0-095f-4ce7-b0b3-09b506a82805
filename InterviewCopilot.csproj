﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>WinExe</OutputType>
		<TargetFramework>net8.0-windows</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UseWPF>true</UseWPF>
		<AssemblyTitle>Interview Copilot</AssemblyTitle>
		<AssemblyDescription>WPF Application for Interview Management</AssemblyDescription>
		<AssemblyVersion>*******</AssemblyVersion>
		<FileVersion>*******</FileVersion>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Refit" Version="7.2.22" />
		<PackageReference Include="Refit.HttpClientFactory" Version="7.2.22" />
		<PackageReference Include="Microsoft.Toolkit.Mvvm" Version="7.1.2" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
		<PackageReference Include="Markdig.Wpf" Version="*******" />
		<PackageReference Include="System.Text.Json" Version="8.0.5" />
		<PackageReference Include="NAudio" Version="2.2.1" />
		<PackageReference Include="Microsoft.CognitiveServices.Speech" Version="1.34.1" />
		<PackageReference Include="NAudio.Wasapi" Version="2.2.1" />
		<PackageReference Include="OpenAI" Version="2.2.0" />
		<PackageReference Include="Anthropic.SDK" Version="4.7.1" />


	</ItemGroup>

</Project>
