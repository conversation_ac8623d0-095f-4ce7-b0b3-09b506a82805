using Microsoft.Toolkit.Mvvm.Input;
using InterviewCopilot.Services;
using InterviewCopilot.Models;
using Microsoft.Extensions.Logging;
using System.Text;

namespace InterviewCopilot.ViewModels;

public class InterviewViewModel : ViewModelBase
{
    private readonly INavigationService _navigationService;
    private readonly IInterviewService _interviewService;
    private readonly ISTTService _sttService;
    private readonly IStreamingAIService _streamingAIService;
    private readonly ILogger<InterviewViewModel> _logger;

    private Interview? _currentInterview;
    private DetailedInterview? _detailedInterview; // Stores detailed interview data from start API
    private string _questionText = "";
    private string _conversationMarkdown = "# Interview Conversation\n\nThe conversation will be displayed here in Markdown format.";
    private readonly StringBuilder _conversationBuilder = new();
    private readonly StringBuilder _currentAnswerBuilder = new();
    private DateTime _lastUIUpdate = DateTime.Now;
    private const int UpdateIntervalMs = 100; // Update UI every 100ms during streaming

    public InterviewViewModel(
        INavigationService navigationService,
        IInterviewService interviewService,
        ISTTService sttService,
        IAIAnswerService aiAnswerService,
        IStreamingAIService streamingAIService,
        ILogger<InterviewViewModel> logger)
    {
        _navigationService = navigationService;
        _interviewService = interviewService;
        _sttService = sttService;
        _streamingAIService = streamingAIService;
        _logger = logger;

        EndInterviewCommand = new AsyncRelayCommand(EndInterviewAsync);
        GoBackCommand = new RelayCommand(GoBack);
        GetAIAnswerCommand = new AsyncRelayCommand(GetAIAnswerAsync, CanGetAIAnswer);

        // Listen for property changes to update command state
        PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(QuestionText) || e.PropertyName == nameof(IsBusy))
            {
                GetAIAnswerCommand.NotifyCanExecuteChanged();
            }
        };

        // Subscribe to STT service events
        _sttService.TextTranscribed += OnTextTranscribed;
    }

    public Interview? CurrentInterview
    {
        get => _currentInterview;
        set => SetProperty(ref _currentInterview, value);
    }

    public DetailedInterview? DetailedInterview
    {
        get => _detailedInterview;
        private set => SetProperty(ref _detailedInterview, value);
    }

    public string QuestionText
    {
        get => _questionText;
        set => SetProperty(ref _questionText, value);
    }

    public string ConversationMarkdown
    {
        get => _conversationMarkdown;
        set => SetProperty(ref _conversationMarkdown, value);
    }

    public string InterviewTitle => CurrentInterview != null
        ? $"Interview #{CurrentInterview.Id}"
        : "Interview";

    public string InterviewDetails => CurrentInterview != null
        ? $"Status: {CurrentInterview.FormattedStatus} | Scheduled: {CurrentInterview.FormattedDateTime} | Duration: {CurrentInterview.FormattedDuration}"
        : string.Empty;

    public IAsyncRelayCommand EndInterviewCommand { get; }
    public IRelayCommand GoBackCommand { get; }
    public IAsyncRelayCommand GetAIAnswerCommand { get; }

    public async Task Initialize(Interview interview)
    {
        CurrentInterview = interview;
        _logger.LogInformation("Initialized interview view for {Id}", interview.Id);

        // Call start interview API to get detailed information
        await StartInterviewSessionAsync(interview.Id);

        // Initialize question text for STT
        QuestionText = "";

        // Start STT service
        await _sttService.StartListeningAsync();

        ConversationMarkdown = $"";

        OnPropertyChanged(nameof(InterviewTitle));
        OnPropertyChanged(nameof(InterviewDetails));
    }

    private async Task StartInterviewSessionAsync(string interviewId)
    {
        try
        {
            _logger.LogInformation("Starting interview session for {InterviewId}", interviewId);

            var detailedInterview = await _interviewService.StartInterviewAsync(interviewId);
            //DetailedInterview = detailedInterview;
            if (detailedInterview != null)
            {
                DetailedInterview = detailedInterview;
                _logger.LogInformation("Successfully retrieved detailed interview data for {InterviewId}", interviewId);

                // Log detailed information (kept in memory, not shown in UI)
                _logger.LogDebug("Interview session started at: {SessionStartedDate}", detailedInterview.SessionStartedDateUtc);
                _logger.LogDebug("Interview type: {InterviewType}", detailedInterview.InterviewTypeDisplayName);

                if (detailedInterview.CandidateInterviewProfile != null)
                {
                    _logger.LogDebug("Candidate profile - Primary Technology: {Technology}, Experience: {Experience} years",
                                   detailedInterview.CandidateInterviewProfile.PrimaryTechnology,
                                   detailedInterview.CandidateInterviewProfile.TotalYearsExperience);
                }

                if (detailedInterview.LlmModel != null)
                {
                    _logger.LogDebug("LLM Model: {ModelName} ({Provider}), Max Tokens: {MaxTokens}",
                                   detailedInterview.LlmModel.DisplayName,
                                   detailedInterview.LlmModel.Provider,
                                   detailedInterview.LlmModel.MaxTokens);
                }
            }
            else
            {
                _logger.LogWarning("Failed to start interview session for {InterviewId}", interviewId);
                SetError("Failed to start interview session. Please try again.");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting interview session for {InterviewId}", interviewId);
            SetError("An error occurred while starting the interview session.");
        }
    }

    private async Task EndInterviewAsync()
    {
        try
        {
            // Stop STT service
            await _sttService.StopListeningAsync();

            if (CurrentInterview != null)
            {
                _logger.LogInformation("Ending interview {Id}", CurrentInterview.Id);

                // Update interview status to completed
                await _interviewService.UpdateInterviewStatusAsync(CurrentInterview.Id, "Completed");
            }

            _navigationService.NavigateTo("Dashboard");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ending interview");
            SetError("An error occurred while ending the interview.");
        }
    }

    private void GoBack()
    {
        _navigationService.NavigateTo("Dashboard");
    }

    public void ClearQuestionText()
    {
        QuestionText = string.Empty;
        _logger.LogInformation("Question text cleared by user");
    }

    private bool CanGetAIAnswer()
    {
        return !IsBusy && !string.IsNullOrWhiteSpace(QuestionText);
    }

    private async Task GetAIAnswerAsync()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(QuestionText))
            {
                SetError("Please enter a question first.");
                return;
            }

            IsBusy = true;
            ClearError();

            var question = QuestionText.Trim();
            _logger.LogInformation("Starting streaming AI answer for question: {Question}", question.Substring(0, Math.Min(50, question.Length)));

            // Clear previous answer and reset builders
            _currentAnswerBuilder.Clear();
            _lastUIUpdate = DateTime.Now;

            // Add question to conversation immediately
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var questionEntry = $"## Interviewer - {timestamp}{question}\n## 🤖 AI Response\n";

            // Start the conversation entry
            if (_conversationBuilder.Length > 0)
            {
                _conversationBuilder.Insert(0, questionEntry + "---\n\n");
            }
            else
            {
                _conversationBuilder.Append(questionEntry);
            }

            // Start streaming AI response directly to conversation
            await foreach (var chunk in _streamingAIService.GetStreamingAnswerAsync(question, DetailedInterview))
            {
                _currentAnswerBuilder.Append(chunk);

                // Throttle UI updates for better performance
                if ((DateTime.Now - _lastUIUpdate).TotalMilliseconds > UpdateIntervalMs)
                {
                    // Update conversation with streaming content
                    UpdateConversationWithStreamingResponse(_currentAnswerBuilder.ToString());
                    _lastUIUpdate = DateTime.Now;
                }
            }

            // Final update with complete response
            var finalAnswer = _currentAnswerBuilder.ToString();
            UpdateConversationWithFinalResponse(finalAnswer);

            // Clear question text after successful answer generation
            QuestionText = "Auto-transcribed text from STT service will appear here...";

            _logger.LogInformation("Streaming AI answer completed successfully");
        }
        catch (Exception ex)
        {
            SetError("Failed to generate AI answer. Please try again.");
            _logger.LogError(ex, "Error generating streaming AI answer");
        }
        finally
        {
            IsBusy = false;
        }
    }

    private void UpdateConversationWithStreamingResponse(string streamingAnswer)
    {
        // Create temporary conversation with current streaming content
        var currentConversation = _conversationBuilder.ToString();
        var tempConversation = currentConversation + streamingAnswer;

        if (_conversationBuilder.Length > 0 && !currentConversation.EndsWith("## 🤖 AI Response\n"))
        {
            tempConversation += "\n\n---\n\n" + _conversationBuilder.ToString();
        }

        ConversationMarkdown = tempConversation;
    }

    private void UpdateConversationWithFinalResponse(string finalAnswer)
    {
        // Update the conversation builder with the final complete response
        var currentContent = _conversationBuilder.ToString();
        var finalContent = currentContent + finalAnswer + "\n\n";

        _conversationBuilder.Clear();
        _conversationBuilder.Append(finalContent);

        ConversationMarkdown = _conversationBuilder.ToString();
    }
    private void OnTextTranscribed(object? sender, string transcribedText)
    {
        try
        {
            _logger.LogDebug("Text transcribed from STT: {Text}", transcribedText);

            // Update question text with transcribed text
            if (QuestionText == "Auto-transcribed text from STT service will appear here...")
            {
                QuestionText = transcribedText;
            }
            else
            {
                QuestionText += " " + transcribedText;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling transcribed text");
        }
    }

    // Method for testing STT functionality
    public void SimulateSTTInput(string text)
    {
        _sttService.SimulateTranscription(text);
    }
}
