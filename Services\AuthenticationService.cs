using InterviewCopilot.Models;
using Microsoft.Extensions.Logging;
using Refit;
using System.Text.Json;
using System.Net.Http;

namespace InterviewCopilot.Services;

public class AuthenticationService : IAuthenticationService
{
    private readonly IAuthenticationApi _authApi;
    private readonly ILogger<AuthenticationService> _logger;
    
    private string? _token;
    private DateTime _tokenExpiration;
    private UserInfo? _currentUser;

    public event EventHandler<bool>? AuthenticationStateChanged;

    public bool IsAuthenticated => !string.IsNullOrEmpty(_token) && _tokenExpiration > DateTime.Now;
    public UserInfo? CurrentUser => _currentUser;
    public string? Token => _token;

    public AuthenticationService(IAuthenticationApi authApi, ILogger<AuthenticationService> logger)
    {
        _authApi = authApi;
        _logger = logger;
    }

    public async Task<bool> LoginAsync(string email, string password)
    {
        try
        {
            _logger.LogInformation("Attempting login for user: {Email}", email);

            var request = new LoginRequest
            {
                Email = email,
                Password = password
            };

            _logger.LogDebug("Sending login request to API");
            var response = await _authApi.LoginAsync(request);

            _logger.LogDebug("Received login response - Success: {Success}, Message: {Message}",
                           response.Success, response.Message);

            if (response.Success && !string.IsNullOrEmpty(response.Token))
            {
                _token = response.Token;
                _tokenExpiration = response.ExpiresAt;
                _currentUser = response.User;

                _logger.LogInformation("User {Email} logged in successfully. Token expires at: {ExpiresAt}",
                                     email, response.ExpiresAt);
                _logger.LogDebug("User details - Name: {FullName}, Role: {Role}",
                               response.User?.FullName, response.User?.Role);

                AuthenticationStateChanged?.Invoke(this, true);
                return true;
            }
            else
            {
                var errorMessage = !string.IsNullOrEmpty(response.Message)
                    ? response.Message
                    : "Login failed - invalid credentials";
                _logger.LogWarning("Login failed for {Email}: {Message}", email, errorMessage);
                return false;
            }
        }
        catch (ApiException ex)
        {
            _logger.LogError(ex, "API error during login for {Email}. Status: {StatusCode}, Content: {Content}",
                           email, ex.StatusCode, ex.Content);
            return false;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "Network error during login for {Email}: {Message}", email, ex.Message);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during login for {Email}", email);
            return false;
        }
    }

    public async Task LogoutAsync()
    {
        try
        {
            if (!string.IsNullOrEmpty(_token))
            {
                await _authApi.LogoutAsync(GetAuthorizationHeader());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
        }
        finally
        {
            _token = null;
            _tokenExpiration = DateTime.MinValue;
            _currentUser = null;
            
            _logger.LogInformation("User logged out");
            AuthenticationStateChanged?.Invoke(this, false);
        }
    }

    public async Task<bool> RefreshTokenAsync()
    {
        // For now, we'll implement a simple token validation
        // In a real application, you might have a refresh token endpoint
        try
        {
            if (!string.IsNullOrEmpty(_token))
            {
                var profile = await _authApi.GetProfileAsync(GetAuthorizationHeader());
                _currentUser = profile;
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing token");
            await LogoutAsync();
        }
        
        return false;
    }

    public string GetAuthorizationHeader()
    {
        return $"Bearer {_token}";
    }
}
