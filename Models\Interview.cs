using System.Text.Json.Serialization;

namespace InterviewCopilot.Models;

public class Interview
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("jobDescription")]
    public string JobDescription { get; set; } = string.Empty;

    [JsonPropertyName("interviewDateTimeUtc")]
    public DateTime InterviewDateTimeUtc { get; set; }

    [JsonPropertyName("interviewDateTimeLocal")]
    public DateTime InterviewDateTimeLocal { get; set; }

    [JsonPropertyName("timeZone")]
    public string TimeZone { get; set; } = string.Empty;

    [JsonPropertyName("status")]
    public int Status { get; set; }

    [JsonPropertyName("statusDisplayName")]
    public string StatusDisplayName { get; set; } = string.Empty;

    [JsonPropertyName("notes")]
    public string Notes { get; set; } = string.Empty;

    [JsonPropertyName("createdDateUtc")]
    public DateTime CreatedDateUtc { get; set; }

    [JsonPropertyName("completedDateUtc")]
    public DateTime? CompletedDateUtc { get; set; }

    [JsonPropertyName("cancelledDateUtc")]
    public DateTime? CancelledDateUtc { get; set; }

    [JsonPropertyName("cancellationReason")]
    public string? CancellationReason { get; set; }

    [JsonPropertyName("isUpcoming")]
    public bool IsUpcoming { get; set; }

    [JsonPropertyName("isPast")]
    public bool IsPast { get; set; }

    // Computed properties for UI - showing minimal information for candidates
    public string JobTitle => !string.IsNullOrEmpty(JobDescription) && JobDescription.Length > 50
        ? JobDescription.Substring(0, 50) + "..."
        : JobDescription;

    public string Company => "Interview"; // Generic company name for candidate privacy
    public string InterviewType => StatusDisplayName; // Use status as interview type
    public int Duration => 60; // Default duration in minutes

    public string FormattedDateTime => InterviewDateTimeLocal.ToString("MMM dd, yyyy 'at' h:mm tt");
    public string FormattedDuration => $"{Duration} minutes";
    public string FormattedStatus => StatusDisplayName;
    public string ShortJobDescription => !string.IsNullOrEmpty(JobDescription) && JobDescription.Length > 100
        ? JobDescription.Substring(0, 100) + "..."
        : JobDescription;

    public bool CanStart => IsUpcoming && InterviewDateTimeLocal <= DateTime.Now.AddDays(15); // TBD: Can start 15 minutes early
}

public class InterviewsResponse
{
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("interviews")]
    public List<Interview> Interviews { get; set; } = new();

    [JsonPropertyName("totalCount")]
    public int TotalCount { get; set; }

    [JsonPropertyName("page")]
    public int Page { get; set; }

    [JsonPropertyName("pageSize")]
    public int PageSize { get; set; }

    [JsonPropertyName("hasNextPage")]
    public bool HasNextPage { get; set; }

    [JsonPropertyName("hasPreviousPage")]
    public bool HasPreviousPage { get; set; }
}

public class ApiError
{
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("details")]
    public string Details { get; set; } = string.Empty;

    [JsonPropertyName("statusCode")]
    public int StatusCode { get; set; }
}
