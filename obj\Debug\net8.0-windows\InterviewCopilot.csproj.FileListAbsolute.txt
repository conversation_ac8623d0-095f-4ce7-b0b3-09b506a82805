C:\code\interview_copilot\obj\Debug\net8.0-windows\InterviewCopilot.csproj.AssemblyReference.cache
C:\code\interview_copilot\obj\Debug\net8.0-windows\App.baml
C:\code\interview_copilot\obj\Debug\net8.0-windows\App.g.cs
C:\code\interview_copilot\obj\Debug\net8.0-windows\GeneratedInternalTypeHelper.g.cs
C:\code\interview_copilot\obj\Debug\net8.0-windows\InterviewCopilot_MarkupCompile.cache
C:\code\interview_copilot\obj\Debug\net8.0-windows\InterviewCopilot_MarkupCompile.lref
C:\code\interview_copilot\obj\Debug\net8.0-windows\Views\LoginView.baml
C:\code\interview_copilot\obj\Debug\net8.0-windows\Views\LoginView.g.cs
C:\code\interview_copilot\obj\Debug\net8.0-windows\Views\DashboardView.baml
C:\code\interview_copilot\obj\Debug\net8.0-windows\Views\DashboardView.g.cs
C:\code\interview_copilot\obj\Debug\net8.0-windows\Views\MainWindow.g.cs
C:\code\interview_copilot\obj\Debug\net8.0-windows\Views\InterviewView.baml
C:\code\interview_copilot\obj\Debug\net8.0-windows\Views\InterviewView.g.cs
C:\code\interview_copilot\bin\Debug\net8.0-windows\InterviewCopilot.exe
C:\code\interview_copilot\bin\Debug\net8.0-windows\InterviewCopilot.deps.json
C:\code\interview_copilot\bin\Debug\net8.0-windows\InterviewCopilot.runtimeconfig.json
C:\code\interview_copilot\bin\Debug\net8.0-windows\InterviewCopilot.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\InterviewCopilot.pdb
C:\code\interview_copilot\bin\Debug\net8.0-windows\Markdig.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Markdig.Wpf.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Binder.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.CommandLine.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Json.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.UserSecrets.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyInjection.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Diagnostics.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Diagnostics.Abstractions.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.FileProviders.Physical.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.FileSystemGlobbing.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Hosting.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Hosting.Abstractions.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Http.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.Configuration.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.Console.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.Debug.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.EventLog.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.EventSource.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Options.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Options.ConfigurationExtensions.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.Primitives.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Toolkit.Mvvm.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Refit.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Refit.HttpClientFactory.dll
C:\code\interview_copilot\obj\Debug\net8.0-windows\Styles\AppStyles.baml
C:\code\interview_copilot\obj\Debug\net8.0-windows\Views\MainWindow.baml
C:\code\interview_copilot\obj\Debug\net8.0-windows\InterviewCopilot.g.resources
C:\code\interview_copilot\obj\Debug\net8.0-windows\InterviewCopilot.GeneratedMSBuildEditorConfig.editorconfig
C:\code\interview_copilot\obj\Debug\net8.0-windows\InterviewCopilot.AssemblyInfoInputs.cache
C:\code\interview_copilot\obj\Debug\net8.0-windows\InterviewCopilot.AssemblyInfo.cs
C:\code\interview_copilot\obj\Debug\net8.0-windows\InterviewCopilot.csproj.CoreCompileInputs.cache
C:\code\interview_copilot\obj\Debug\net8.0-windows\Intervie.68FE0768.Up2Date
C:\code\interview_copilot\obj\Debug\net8.0-windows\InterviewCopilot.dll
C:\code\interview_copilot\obj\Debug\net8.0-windows\refint\InterviewCopilot.dll
C:\code\interview_copilot\obj\Debug\net8.0-windows\InterviewCopilot.pdb
C:\code\interview_copilot\obj\Debug\net8.0-windows\InterviewCopilot.genruntimeconfig.cache
C:\code\interview_copilot\obj\Debug\net8.0-windows\ref\InterviewCopilot.dll
C:\code\interview_copilot\obj\Debug\net8.0-windows\InterviewCopilot.sourcelink.json
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.CognitiveServices.Speech.csharp.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\NAudio.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\NAudio.Asio.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\NAudio.Core.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\NAudio.Midi.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\NAudio.Wasapi.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\NAudio.WinForms.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\NAudio.WinMM.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\android-x86\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\centos7-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\ios-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\iossimulator-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\iossimulator-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-arm\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.core.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.codec.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.kws.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libMicrosoft.CognitiveServices.Speech.extension.lu.so
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\maccatalyst-arm64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\maccatalyst-x64\native\libMicrosoft.CognitiveServices.Speech.core.a
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\osx-arm64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\osx-arm64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\osx-x64\lib\netstandard2.0\Microsoft.CognitiveServices.Speech.csharp.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\osx-x64\native\libMicrosoft.CognitiveServices.Speech.core.dylib
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.core.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-arm\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.core.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-arm64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.core.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-x64\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.core.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.codec.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\runtimes\win-x86\native\Microsoft.CognitiveServices.Speech.extension.lu.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Anthropic.SDK.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Bcl.AsyncInterfaces.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\Microsoft.Extensions.AI.Abstractions.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\OpenAI.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\System.ClientModel.dll
C:\code\interview_copilot\bin\Debug\net8.0-windows\System.Memory.Data.dll
