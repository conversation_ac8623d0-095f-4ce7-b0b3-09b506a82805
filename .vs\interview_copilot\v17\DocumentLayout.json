{"Version": 1, "WorkspaceRootPath": "C:\\code\\interview_copilot\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|c:\\code\\interview_copilot\\views\\interviewview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|solutionrelative:views\\interviewview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|c:\\code\\interview_copilot\\viewmodels\\dashboardviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|solutionrelative:viewmodels\\dashboardviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|c:\\code\\interview_copilot\\viewmodels\\interviewviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|solutionrelative:viewmodels\\interviewviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|c:\\code\\interview_copilot\\services\\interviewservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|solutionrelative:services\\interviewservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|c:\\code\\interview_copilot\\interview_screen_enhancements.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|solutionrelative:interview_screen_enhancements.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|c:\\code\\interview_copilot\\services\\streamingaiservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|solutionrelative:services\\streamingaiservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|c:\\code\\interview_copilot\\services\\sttservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|solutionrelative:services\\sttservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|c:\\code\\interview_copilot\\viewmodels\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|solutionrelative:viewmodels\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|c:\\code\\interview_copilot\\services\\aianswerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|solutionrelative:services\\aianswerservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|c:\\code\\interview_copilot\\services\\anthropicmessanger.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|solutionrelative:services\\anthropicmessanger.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|c:\\code\\interview_copilot\\services\\gptmessanger.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|solutionrelative:services\\gptmessanger.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|c:\\code\\interview_copilot\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|c:\\code\\interview_copilot\\services\\isttservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|solutionrelative:services\\isttservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|c:\\code\\interview_copilot\\interviewcopilot.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|solutionrelative:interviewcopilot.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|c:\\code\\interview_copilot\\viewmodels\\loginviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{792D3578-7B8E-2A73-29B4-3022953E8B5D}|InterviewCopilot.csproj|solutionrelative:viewmodels\\loginviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Document", "DocumentIndex": 4, "Title": "INTERVIEW_SCREEN_ENHANCEMENTS.md", "DocumentMoniker": "C:\\code\\interview_copilot\\INTERVIEW_SCREEN_ENHANCEMENTS.md", "RelativeDocumentMoniker": "INTERVIEW_SCREEN_ENHANCEMENTS.md", "ToolTip": "C:\\code\\interview_copilot\\INTERVIEW_SCREEN_ENHANCEMENTS.md", "RelativeToolTip": "INTERVIEW_SCREEN_ENHANCEMENTS.md", "ViewState": "AgIAAL0AAAAAAAAAAAAAANQAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-22T02:13:12.156Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "InterviewService.cs", "DocumentMoniker": "C:\\code\\interview_copilot\\Services\\InterviewService.cs", "RelativeDocumentMoniker": "Services\\InterviewService.cs", "ToolTip": "C:\\code\\interview_copilot\\Services\\InterviewService.cs", "RelativeToolTip": "Services\\InterviewService.cs", "ViewState": "AgIAAGMAAAAAAAAAAAAQwHoAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T02:06:17.082Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "MainWindowViewModel.cs", "DocumentMoniker": "C:\\code\\interview_copilot\\ViewModels\\MainWindowViewModel.cs", "RelativeDocumentMoniker": "ViewModels\\MainWindowViewModel.cs", "ToolTip": "C:\\code\\interview_copilot\\ViewModels\\MainWindowViewModel.cs", "RelativeToolTip": "ViewModels\\MainWindowViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAF0AAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T01:37:39.321Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "StreamingAIService.cs", "DocumentMoniker": "C:\\code\\interview_copilot\\Services\\StreamingAIService.cs", "RelativeDocumentMoniker": "Services\\StreamingAIService.cs", "ToolTip": "C:\\code\\interview_copilot\\Services\\StreamingAIService.cs", "RelativeToolTip": "Services\\StreamingAIService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T23:43:12.623Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "InterviewView.xaml", "DocumentMoniker": "C:\\code\\interview_copilot\\Views\\InterviewView.xaml", "RelativeDocumentMoniker": "Views\\InterviewView.xaml", "ToolTip": "C:\\code\\interview_copilot\\Views\\InterviewView.xaml", "RelativeToolTip": "Views\\InterviewView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-21T23:39:52.706Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "ISTTService.cs", "DocumentMoniker": "C:\\code\\interview_copilot\\Services\\ISTTService.cs", "RelativeDocumentMoniker": "Services\\ISTTService.cs", "ToolTip": "C:\\code\\interview_copilot\\Services\\ISTTService.cs", "RelativeToolTip": "Services\\ISTTService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T23:29:30.447Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "AIAnswerService.cs", "DocumentMoniker": "C:\\code\\interview_copilot\\Services\\AIAnswerService.cs", "RelativeDocumentMoniker": "Services\\AIAnswerService.cs", "ToolTip": "C:\\code\\interview_copilot\\Services\\AIAnswerService.cs", "RelativeToolTip": "Services\\AIAnswerService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T23:29:02.525Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "GptMessanger.cs", "DocumentMoniker": "C:\\code\\interview_copilot\\Services\\GptMessanger.cs", "RelativeDocumentMoniker": "Services\\GptMessanger.cs", "ToolTip": "C:\\code\\interview_copilot\\Services\\GptMessanger.cs", "RelativeToolTip": "Services\\GptMessanger.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAQwBQAAABgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T23:27:52.788Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "InterviewViewModel.cs", "DocumentMoniker": "C:\\code\\interview_copilot\\ViewModels\\InterviewViewModel.cs", "RelativeDocumentMoniker": "ViewModels\\InterviewViewModel.cs", "ToolTip": "C:\\code\\interview_copilot\\ViewModels\\InterviewViewModel.cs", "RelativeToolTip": "ViewModels\\InterviewViewModel.cs", "ViewState": "AgIAALMAAAAAAAAAAAASwMQAAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T23:13:49.527Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "appsettings.json", "DocumentMoniker": "C:\\code\\interview_copilot\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "C:\\code\\interview_copilot\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-21T23:40:12.913Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "AnthropicMessanger.cs", "DocumentMoniker": "C:\\code\\interview_copilot\\Services\\AnthropicMessanger.cs", "RelativeDocumentMoniker": "Services\\AnthropicMessanger.cs", "ToolTip": "C:\\code\\interview_copilot\\Services\\AnthropicMessanger.cs", "RelativeToolTip": "Services\\AnthropicMessanger.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T23:27:23.419Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "LoginViewModel.cs", "DocumentMoniker": "C:\\code\\interview_copilot\\ViewModels\\LoginViewModel.cs", "RelativeDocumentMoniker": "ViewModels\\LoginViewModel.cs", "ToolTip": "C:\\code\\interview_copilot\\ViewModels\\LoginViewModel.cs", "RelativeToolTip": "ViewModels\\LoginViewModel.cs", "ViewState": "AgIAADIAAAAAAAAAAAAawE4AAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T23:24:10.8Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "DashboardViewModel.cs", "DocumentMoniker": "C:\\code\\interview_copilot\\ViewModels\\DashboardViewModel.cs", "RelativeDocumentMoniker": "ViewModels\\DashboardViewModel.cs", "ToolTip": "C:\\code\\interview_copilot\\ViewModels\\DashboardViewModel.cs", "RelativeToolTip": "ViewModels\\DashboardViewModel.cs", "ViewState": "AgIAAGAAAAAAAAAAAAAawHwAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T23:24:10.811Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "STTService.cs", "DocumentMoniker": "C:\\code\\interview_copilot\\Services\\STTService.cs", "RelativeDocumentMoniker": "Services\\STTService.cs", "ToolTip": "C:\\code\\interview_copilot\\Services\\STTService.cs", "RelativeToolTip": "Services\\STTService.cs", "ViewState": "AgIAAN8AAAAAAAAAAAAQwPMAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T23:18:34.538Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "InterviewCop<PERSON>t", "DocumentMoniker": "C:\\code\\interview_copilot\\InterviewCopilot.csproj", "RelativeDocumentMoniker": "InterviewCopilot.csproj", "ToolTip": "C:\\code\\interview_copilot\\InterviewCopilot.csproj", "RelativeToolTip": "InterviewCopilot.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-21T23:14:28.484Z", "EditorCaption": ""}]}]}]}