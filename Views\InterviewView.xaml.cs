using System.Windows.Controls;
using System.Windows.Input;
using InterviewCopilot.ViewModels;

namespace InterviewCopilot.Views;

public partial class InterviewView : UserControl
{
    public InterviewView()
    {
        InitializeComponent();
    }

    private void QuestionTextBox_MouseDoubleClick(object sender, MouseButtonEventArgs e)
    {
        if (DataContext is InterviewViewModel viewModel)
        {
            viewModel.ClearQuestionText();
        }
    }
}
