# API Integration Update - Interviews Endpoint

This document outlines the updates made to integrate with the new interviews API endpoint and display minimal candidate information.

## API Endpoint Changes

### Updated Base URL
- **Local**: `https://localhost:7243/api/`
- **Current**: `https://unifiedhrm.com/api/`

### Interviews API Details
- **Endpoint**: `GET https://unifiedhrm.com/api/consultant/interviews`
- **Authorization**: `<PERSON><PERSON>` (in Authorization header)
- **Response Format**: Matches the provided API specification exactly

## Model Updates

### Interview Model Enhancements
The `Interview` model now includes all fields from the API response:

```csharp
public class Interview
{
    [JsonPropertyName("id")]
    public string Id { get; set; }
    
    [JsonPropertyName("jobDescription")]
    public string JobDescription { get; set; }
    
    [JsonPropertyName("interviewDateTimeUtc")]
    public DateTime InterviewDateTimeUtc { get; set; }
    
    [JsonPropertyName("interviewDateTimeLocal")]
    public DateTime InterviewDateTimeLocal { get; set; }
    
    [JsonPropertyName("timeZone")]
    public string TimeZone { get; set; }
    
    [JsonPropertyName("status")]
    public int Status { get; set; }
    
    [JsonPropertyName("statusDisplayName")]
    public string StatusDisplayName { get; set; }
    
    [JsonPropertyName("notes")]
    public string Notes { get; set; }
    
    [JsonPropertyName("isUpcoming")]
    public bool IsUpcoming { get; set; }
    
    [JsonPropertyName("isPast")]
    public bool IsPast { get; set; }
    
    // Additional fields for completeness
    [JsonPropertyName("createdDateUtc")]
    public DateTime CreatedDateUtc { get; set; }
    
    [JsonPropertyName("completedDateUtc")]
    public DateTime? CompletedDateUtc { get; set; }
    
    [JsonPropertyName("cancelledDateUtc")]
    public DateTime? CancelledDateUtc { get; set; }
    
    [JsonPropertyName("cancellationReason")]
    public string? CancellationReason { get; set; }
}
```

### Computed Properties for UI (Minimal Information)
To protect candidate privacy, the UI shows minimal information:

```csharp
// Shows truncated job description as title
public string JobTitle => !string.IsNullOrEmpty(JobDescription) && JobDescription.Length > 50 
    ? JobDescription.Substring(0, 50) + "..." 
    : JobDescription;

// Generic company name for privacy
public string Company => "Interview";

// Use status as interview type
public string InterviewType => StatusDisplayName;

// Formatted display properties
public string FormattedDateTime => InterviewDateTimeLocal.ToString("MMM dd, yyyy 'at' h:mm tt");
public string FormattedStatus => StatusDisplayName;
public string ShortJobDescription => !string.IsNullOrEmpty(JobDescription) && JobDescription.Length > 100 
    ? JobDescription.Substring(0, 100) + "..." 
    : JobDescription;
```

## UI Updates

### Dashboard View Improvements
The dashboard now displays:

1. **Interview ID**: Shows "Interview #[ID]" instead of job title
2. **Status Badge**: Color-coded status indicator
3. **Date/Time**: Formatted local date and time
4. **Duration**: Default 60 minutes
5. **Job Description**: Truncated for privacy (100 characters max)
6. **Notes**: Displayed only if available

### Interview Screen Updates
When starting an interview, the screen now shows:

1. **Interview Header**: Interview ID and status
2. **Question Area**: 
   - Interview details (ID, status, date/time)
   - Full job description
   - Notes (if available)
   - Placeholder for future AI questions

3. **Conversation Area**: 
   - Interview metadata
   - Job description
   - Placeholder for conversation transcription

## Privacy Considerations

### Minimal Information Display
- **No candidate names** or personal information shown
- **Generic company name** ("Interview") used
- **Truncated job descriptions** to show only essential details
- **Interview ID** used as primary identifier

### Data Protection
- Only essential interview information is displayed
- Full details available only when interview is started
- Notes and descriptions are truncated in list view

## API Response Handling

### Success Response Structure
```json
{
    "success": true,
    "message": "Interviews retrieved successfully",
    "interviews": [...],
    "totalCount": 1,
    "page": 1,
    "pageSize": 50,
    "hasNextPage": false,
    "hasPreviousPage": false
}
```

### Error Handling
- Checks `success` field in API response
- Displays appropriate error messages
- Logs detailed error information for debugging
- Handles network connectivity issues

## Testing Checklist

### Authentication Flow
- [ ] Login with provided credentials
- [ ] Token stored and used for interviews API
- [ ] Proper error handling for authentication failures

### Interviews Display
- [ ] Interviews loaded from new API endpoint
- [ ] Minimal information displayed for privacy
- [ ] Interview ID shown as primary identifier
- [ ] Status badge displayed correctly
- [ ] Date/time formatted properly
- [ ] Job description truncated appropriately

### Interview Screen
- [ ] Interview details displayed when started
- [ ] Full job description shown in question area
- [ ] Notes displayed if available
- [ ] Conversation area shows interview metadata

### Error Scenarios
- [ ] No interviews available message
- [ ] API connection errors handled gracefully
- [ ] Invalid token scenarios handled
- [ ] Network timeout scenarios handled

## Configuration

### API Endpoints
- **Authentication**: `https://localhost:7243/api/consultant/login`
- **Interviews**: `https://unifiedhrm.com/api/consultant/interviews`

### Headers
- **Content-Type**: `application/json`
- **Authorization**: `Bearer {token}`

## Future Enhancements

1. **Real-time Updates**: Implement SignalR for live interview updates
2. **Candidate Information**: Add secure candidate details when needed
3. **Interview Recording**: Integrate conversation transcription
4. **AI Questions**: Implement dynamic question generation
5. **Analytics**: Add interview performance tracking

## Security Notes

- SSL certificate validation configured for development
- Token-based authentication implemented
- Minimal data exposure for candidate privacy
- Secure API communication established
