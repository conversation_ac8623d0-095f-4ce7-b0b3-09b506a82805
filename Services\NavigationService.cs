using Microsoft.Extensions.Logging;

namespace InterviewCopilot.Services;

public class NavigationService : INavigationService
{
    private readonly ILogger<NavigationService> _logger;
    private readonly Stack<string> _navigationHistory = new();

    public event EventHandler<NavigationEventArgs>? NavigationRequested;

    public bool CanGoBack => _navigationHistory.Count > 1;

    public NavigationService(ILogger<NavigationService> logger)
    {
        _logger = logger;
    }

    public void NavigateTo(string viewName)
    {
        NavigateTo(viewName, null);
    }

    public void NavigateTo(string viewName, object? parameter)
    {
        _logger.LogInformation("Navigating to {ViewName} with parameter: {HasParameter}", viewName, parameter != null);

        _navigationHistory.Push(viewName);
        NavigationRequested?.Invoke(this, new NavigationEventArgs(viewName, parameter));
    }

    public void GoBack()
    {
        if (CanGoBack)
        {
            _navigationHistory.Pop(); // Remove current page
            var previousPage = _navigationHistory.Peek();

            _logger.LogInformation("Navigating back to {ViewName}", previousPage);
            NavigationRequested?.Invoke(this, new NavigationEventArgs(previousPage));
        }
    }
}
