{"AzureSpeechServices": {"SubscriptionKey": "YOUR_AZURE_SPEECH_API_KEY", "Region": "YOUR_AZURE_REGION", "Language": "en-US"}, "SpeechRecognition": {"SampleRate": 16000, "BitsPerSample": 16, "Channels": 1, "BufferDurationSeconds": 20, "ResamplerQuality": 60, "BufferSizeMs": 40}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "InterviewCopilot.Services.STTService": "Debug", "InterviewCopilot.Services.AIAnswerService": "Debug"}}}