# Azure Speech Services STT Implementation

This document outlines the implementation of Azure Speech Services for real-time Speech-to-Text (STT) functionality in the Interview Copilot application, based on the provided Azure Speech Services integration approach.

## Overview

The implementation captures system audio output (loopback capture) and processes it through Azure Speech Services for real-time speech recognition. This allows the application to transcribe speech from any audio source playing on the system.

## Architecture Components

### 1. Azure Speech Services Integration
- **Microsoft.CognitiveServices.Speech**: Azure Speech SDK
- **Real-time Recognition**: Continuous speech recognition
- **Audio Streaming**: Push audio stream to Azure services

### 2. Audio Capture System
- **NAudio.Wasapi**: Windows Audio Session API for loopback capture
- **MediaFoundation**: Audio format conversion and resampling
- **BufferedWaveProvider**: Audio buffering for smooth processing

### 3. Service Implementation

#### STTService Class
```csharp
public class STTService : ISTTService, IDisposable
{
    // Azure Speech Services components
    private SpeechRecognizer? _recognizer;
    private PushAudioInputStream? _pushStream;
    
    // NAudio components for audio capture
    private WasapiLoopbackCapture? _capture;
    private BufferedWaveProvider? _bufferedWaveProvider;
    private MediaFoundationResampler? _resampler;
}
```

## Implementation Details

### 1. Audio Capture Setup

#### Loopback Capture Configuration
```csharp
// Get default audio output device for loopback capture
var enumerator = new MMDeviceEnumerator();
var device = enumerator.GetDefaultAudioEndpoint(DataFlow.Render, Role.Multimedia);

// Create loopback capture
_capture = new WasapiLoopbackCapture(device);
```

#### Audio Format Conversion
```csharp
var inputFormat = _capture.WaveFormat;           // System audio format (usually 48kHz stereo)
var desiredFormat = new WaveFormat(16000, 16, 1); // Azure Speech format (16kHz mono)

// Create resampler for format conversion
_resampler = new MediaFoundationResampler(_bufferedWaveProvider, desiredFormat)
{
    ResamplerQuality = 60
};
```

### 2. Azure Speech Services Configuration

#### Speech Config Setup
```csharp
var config = SpeechConfig.FromSubscription(
    SpeechConfiguration.TextToSpeechAPIKey, 
    SpeechConfiguration.TextToSpeechRegion);
config.SpeechRecognitionLanguage = "en-US";
```

#### Audio Stream Configuration
```csharp
var audioFormat = AudioStreamFormat.GetWaveFormatPCM(16000, 16, 1);
_pushStream = AudioInputStream.CreatePushStream(audioFormat) as PushAudioInputStream;
var audioConfig = AudioConfig.FromStreamInput(_pushStream);
```

### 3. Real-time Audio Processing

#### Audio Processing Pipeline
1. **Capture**: System audio captured via WASAPI loopback
2. **Buffer**: Audio data buffered for smooth processing
3. **Resample**: Convert from system format to Azure Speech format
4. **Stream**: Push resampled audio to Azure Speech Services
5. **Recognize**: Azure processes audio and returns text

#### Processing Loop
```csharp
private async Task ProcessAudioAsync(CancellationToken cancellationToken)
{
    var buffer = new byte[bufferSize];
    
    while (!cancellationToken.IsCancellationRequested && 
           _capture?.CaptureState == CaptureState.Capturing)
    {
        int bytesRead = _resampler?.Read(buffer, 0, buffer.Length) ?? 0;
        if (bytesRead > 0)
        {
            _pushStream?.Write(buffer, bytesRead);
        }
        
        await Task.Delay(20, cancellationToken); // 20ms processing interval
    }
}
```

### 4. Event Handling

#### Speech Recognition Events
```csharp
_recognizer.Recognized += OnSpeechRecognized;
_recognizer.Canceled += OnSpeechCanceled;
_recognizer.SessionStarted += OnSessionStarted;
_recognizer.SessionStopped += OnSessionStopped;
```

#### Text Transcription Event
```csharp
private void OnSpeechRecognized(object? sender, SpeechRecognitionEventArgs e)
{
    if (!string.IsNullOrEmpty(e.Result.Text))
    {
        Application.Current?.Dispatcher.Invoke(() =>
        {
            TextTranscribed?.Invoke(this, e.Result.Text);
        });
    }
}
```

## Configuration

### 1. Azure Speech Services Settings
```json
{
  "AzureSpeechServices": {
    "SubscriptionKey": "YOUR_AZURE_SPEECH_API_KEY",
    "Region": "eastus",
    "Language": "en-US"
  }
}
```

### 2. Audio Processing Settings
```json
{
  "SpeechRecognition": {
    "SampleRate": 16000,
    "BitsPerSample": 16,
    "Channels": 1,
    "BufferDurationSeconds": 20,
    "ResamplerQuality": 60,
    "BufferSizeMs": 40
  }
}
```

### 3. SpeechConfiguration Class
```csharp
public static class SpeechConfiguration
{
    public static string TextToSpeechAPIKey => 
        _configuration?["AzureSpeechServices:SubscriptionKey"] ?? "fallback_key";
    
    public static string TextToSpeechRegion => 
        _configuration?["AzureSpeechServices:Region"] ?? "eastus";
    
    public static int SampleRate => 
        _configuration?.GetValue<int>("SpeechRecognition:SampleRate") ?? 16000;
}
```

## Integration with Interview Application

### 1. Service Registration
```csharp
// In App.xaml.cs
services.AddSingleton<ISTTService, STTService>();
```

### 2. ViewModel Integration
```csharp
public class InterviewViewModel
{
    private readonly ISTTService _sttService;
    
    public async Task Initialize(Interview interview)
    {
        // Start STT service when interview begins
        await _sttService.StartListeningAsync();
    }
    
    private void OnTextTranscribed(object? sender, string transcribedText)
    {
        // Update question text with transcribed speech
        QuestionText = transcribedText;
    }
}
```

### 3. UI Integration
- **Question TextBox**: Displays transcribed text
- **Double-click to Clear**: Clears transcribed text
- **STT Status Indicator**: Shows listening status
- **Real-time Updates**: Text appears as speech is recognized

## AI Answer Generation Service

### 1. IAIAnswerService Interface
```csharp
public interface IAIAnswerService
{
    Task<string> GenerateAnswerAsync(string question, DetailedInterview? interviewContext = null);
    Task<string> GenerateAnswerAsync(string question, string jobDescription, CandidateInterviewProfile? candidateProfile = null);
    bool IsConfigured { get; }
}
```

### 2. Contextual Answer Generation
- **Question Analysis**: Determines question type (behavioral, technical, etc.)
- **STAR Method Framework**: Provides structured response guidance
- **Personalized Guidance**: Uses candidate profile for tailored advice
- **Job Relevance**: Connects answers to specific role requirements

### 3. Answer Structure
```markdown
# 🤖 AI Generated Answer
*Generated at [timestamp]*

## 📋 Question Analysis
**Question:** "[question]"
**Question Type:** [Behavioral/Technical/etc.]

## 💡 Suggested Response Strategy
### 👤 Personalized Guidance
- Primary Technology: [technology]
- Experience Level: [years] years

### 🌟 STAR Method Framework
1. Situation - Set context
2. Task - Describe objective
3. Action - Explain steps taken
4. Result - Share outcomes

### 🎯 Key Points to Address
- [Specific guidance points]

### 📝 Sample Response Structure
[Tailored example response]
```

## Error Handling and Logging

### 1. Comprehensive Error Handling
- **Azure Service Errors**: Connection, authentication, quota issues
- **Audio Capture Errors**: Device unavailable, format issues
- **Processing Errors**: Resampling, buffering problems

### 2. Detailed Logging
```csharp
_logger.LogInformation("STT service started successfully");
_logger.LogDebug("Speech recognized: {Text}", e.Result.Text);
_logger.LogError(ex, "Error in audio processing task");
```

### 3. Graceful Degradation
- **Fallback Responses**: When AI service unavailable
- **Simulation Mode**: For testing without Azure services
- **User Feedback**: Clear error messages and status indicators

## Performance Considerations

### 1. Real-time Processing
- **40ms Buffer**: Optimized for real-time speech recognition
- **Async Operations**: Non-blocking audio processing
- **Resource Management**: Proper disposal of audio resources

### 2. Memory Management
- **Buffered Audio**: 20-second buffer for smooth processing
- **Stream Management**: Proper cleanup of audio streams
- **Cancellation Tokens**: Graceful shutdown of processing tasks

### 3. Network Optimization
- **Continuous Recognition**: Maintains persistent connection to Azure
- **Efficient Streaming**: Optimized audio data transmission
- **Error Recovery**: Automatic reconnection on network issues

## Testing and Debugging

### 1. Simulation Methods
```csharp
// For testing STT functionality
_sttService.SimulateTranscription("Tell me about your experience with C#");
```

### 2. Debug Logging
- **Audio Format Information**: Input/output format details
- **Processing Metrics**: Buffer sizes, processing times
- **Recognition Results**: Transcribed text and confidence scores

### 3. Configuration Validation
- **API Key Validation**: Checks for valid Azure credentials
- **Audio Device Validation**: Ensures audio capture device availability
- **Format Compatibility**: Validates audio format conversions

## Security Considerations

### 1. API Key Management
- **Secure Storage**: Keys stored in configuration, not code
- **Environment Variables**: Production keys from secure sources
- **Key Rotation**: Support for updating API keys

### 2. Audio Privacy
- **Loopback Capture**: Only captures system audio output
- **No Recording**: Audio not saved, only processed in real-time
- **Secure Transmission**: Audio streamed securely to Azure

### 3. Data Protection
- **Temporary Processing**: Audio data not persisted
- **Secure Disposal**: Proper cleanup of audio buffers
- **Compliance**: Follows data protection best practices
