{"version": 3, "targets": {"net8.0-windows7.0": {"Anthropic.SDK/4.7.1": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.AI.Abstractions": "9.3.0-preview.1.25114.11"}, "compile": {"lib/net8.0/Anthropic.SDK.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Anthropic.SDK.dll": {"related": ".xml"}}}, "Markdig/0.22.0": {"type": "package", "compile": {"lib/netcoreapp3.1/Markdig.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Markdig.dll": {"related": ".xml"}}}, "Markdig.Wpf/*******": {"type": "package", "dependencies": {"Markdig": "0.22.0"}, "compile": {"lib/net5.0-windows7.0/Markdig.Wpf.dll": {}}, "runtime": {"lib/net5.0-windows7.0/Markdig.Wpf.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.CognitiveServices.Speech/1.34.1": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"related": ".xml"}}, "build": {"build/Microsoft.CognitiveServices.Speech.props": {}}, "runtimeTargets": {"runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "centos7-x64"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "centos7-x64"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"assetType": "native", "rid": "centos7-x64"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "centos7-x64"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "centos7-x64"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "centos7-x64"}, "runtimes/ios-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"assetType": "native", "rid": "ios-arm64"}, "runtimes/iossimulator-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"assetType": "native", "rid": "iossimulator-arm64"}, "runtimes/iossimulator-x64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"assetType": "native", "rid": "iossimulator-x64"}, "runtimes/linux-arm/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assetType": "runtime", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assetType": "runtime", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assetType": "runtime", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assetType": "runtime", "rid": "osx-arm64"}, "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.core.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assetType": "runtime", "rid": "osx-x64"}, "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.core.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.core.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.core.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.core.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.codec.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.core.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.codec.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.Extensions.AI.Abstractions/9.3.0-preview.1.25114.11": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.AI.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.AI.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.props": {}, "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.targets": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Hosting/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.Extensions.Logging.Debug": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "Microsoft.Extensions.Logging.EventSource": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Http/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.3": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Console/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Text.Json": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.EventLog": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0", "System.Text.Json": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Toolkit.Mvvm/7.1.2": {"type": "package", "compile": {"lib/net5.0/Microsoft.Toolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0/Microsoft.Toolkit.Mvvm.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "NAudio/2.2.1": {"type": "package", "dependencies": {"NAudio.Asio": "2.2.1", "NAudio.Core": "2.2.1", "NAudio.Midi": "2.2.1", "NAudio.Wasapi": "2.2.1", "NAudio.WinForms": "2.2.1", "NAudio.WinMM": "2.2.1"}, "compile": {"lib/net6.0-windows7.0/NAudio.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0-windows7.0/NAudio.dll": {"related": ".xml"}}}, "NAudio.Asio/2.2.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NAudio.Core": "2.2.1"}, "compile": {"lib/netstandard2.0/NAudio.Asio.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.Asio.dll": {"related": ".xml"}}}, "NAudio.Core/2.2.1": {"type": "package", "compile": {"lib/netstandard2.0/NAudio.Core.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.Core.dll": {"related": ".xml"}}}, "NAudio.Midi/2.2.1": {"type": "package", "dependencies": {"NAudio.Core": "2.2.1"}, "compile": {"lib/netstandard2.0/NAudio.Midi.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.Midi.dll": {"related": ".xml"}}}, "NAudio.Wasapi/2.2.1": {"type": "package", "dependencies": {"NAudio.Core": "2.2.1"}, "compile": {"lib/netstandard2.0/NAudio.Wasapi.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.Wasapi.dll": {"related": ".xml"}}}, "NAudio.WinForms/2.2.1": {"type": "package", "dependencies": {"NAudio.WinMM": "2.2.1"}, "compile": {"lib/netcoreapp3.1/NAudio.WinForms.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/NAudio.WinForms.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WindowsForms"]}, "NAudio.WinMM/2.2.1": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NAudio.Core": "2.2.1"}, "compile": {"lib/netstandard2.0/NAudio.WinMM.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NAudio.WinMM.dll": {"related": ".xml"}}}, "OpenAI/2.2.0": {"type": "package", "dependencies": {"System.ClientModel": "1.4.2"}, "compile": {"lib/net8.0/OpenAI.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/OpenAI.dll": {"related": ".xml"}}}, "Refit/7.2.22": {"type": "package", "compile": {"lib/net8.0/Refit.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Refit.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/refit.props": {}, "buildTransitive/netstandard2.0/refit.targets": {}}}, "Refit.HttpClientFactory/7.2.22": {"type": "package", "dependencies": {"Microsoft.Extensions.Http": "8.0.0", "Refit": "7.2.22"}, "compile": {"lib/net8.0/Refit.HttpClientFactory.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Refit.HttpClientFactory.dll": {"related": ".xml"}}}, "System.ClientModel/1.4.2": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.3", "System.Memory.Data": "6.0.1"}, "compile": {"lib/net8.0/System.ClientModel.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.ClientModel.dll": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Memory.Data/6.0.1": {"type": "package", "compile": {"lib/net6.0/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Memory.Data.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Security.AccessControl/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Json/8.0.5": {"type": "package", "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}}}, "libraries": {"Anthropic.SDK/4.7.1": {"sha512": "23rtsMCWAJlKpR3av+o8jW34+16VhwvYaMkg3iRHBzUdBbdBKCKmVZeFi9sUHPjdFdOyWPCkjKVLzs7M7m3GFg==", "type": "package", "path": "anthropic.sdk/4.7.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "anthropic.sdk.4.7.1.nupkg.sha512", "anthropic.sdk.nuspec", "icon.png", "lib/net6.0/Anthropic.SDK.dll", "lib/net6.0/Anthropic.SDK.xml", "lib/net8.0/Anthropic.SDK.dll", "lib/net8.0/Anthropic.SDK.xml", "lib/netstandard2.0/Anthropic.SDK.dll", "lib/netstandard2.0/Anthropic.SDK.xml"]}, "Markdig/0.22.0": {"sha512": "/GbIsc1Dc/fOPqJM2sWIlWymgRAVfJhet+MY1Ze46HpDMyl+9TTfs4AdYbXx5QZo7eRh+Ew7A/nyGSVNCuYd9g==", "type": "package", "path": "markdig/0.22.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net452/Markdig.dll", "lib/net452/Markdig.xml", "lib/netcoreapp2.1/Markdig.dll", "lib/netcoreapp2.1/Markdig.xml", "lib/netcoreapp3.1/Markdig.dll", "lib/netcoreapp3.1/Markdig.xml", "lib/netstandard2.0/Markdig.dll", "lib/netstandard2.0/Markdig.xml", "lib/netstandard2.1/Markdig.dll", "lib/netstandard2.1/Markdig.xml", "markdig.0.22.0.nupkg.sha512", "markdig.nuspec"]}, "Markdig.Wpf/*******": {"sha512": "lQRxu90ZqCATHTvd25yR/MxXABYA+Xcp7tmKW5jReTIgegB+AcU0+cd1GqdpM5k2KRVaD8BkBYQFdDw0lquT4w==", "type": "package", "path": "markdig.wpf/*******", "files": [".nupkg.metadata", ".signature.p7s", "lib/net452/Markdig.Wpf.dll", "lib/net5.0-windows7.0/Markdig.Wpf.dll", "lib/netcoreapp3.1/Markdig.Wpf.dll", "markdig.wpf.*******.nupkg.sha512", "markdig.wpf.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"sha512": "3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.CognitiveServices.Speech/1.34.1": {"sha512": "YXjkR8JknEcDAG+j3uh/9Ah6pyCSWzJl0v6Gt38j978ZfIKp6cHl76EDH6F6sKqqe9zuHCht5MyfMpZkSrVFqg==", "type": "package", "path": "microsoft.cognitiveservices.speech/1.34.1", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.CognitiveServices.Speech.props", "build/REDIST.txt", "build/Xamarin.iOS/Microsoft.CognitiveServices.Speech.targets", "build/Xamarin.iOS/libMicrosoft.CognitiveServices.Speech.core.a", "build/monoandroid/Microsoft.CognitiveServices.Speech.targets", "build/monoandroid/libs/arm64-v8a/libMicrosoft.CognitiveServices.Speech.core.so", "build/monoandroid/libs/arm64-v8a/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "build/monoandroid/libs/arm64-v8a/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "build/monoandroid/libs/arm64-v8a/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "build/monoandroid/libs/arm64-v8a/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "build/monoandroid/libs/armeabi-v7a/libMicrosoft.CognitiveServices.Speech.core.so", "build/monoandroid/libs/armeabi-v7a/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "build/monoandroid/libs/armeabi-v7a/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "build/monoandroid/libs/armeabi-v7a/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "build/monoandroid/libs/armeabi-v7a/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "build/monoandroid/libs/x86/libMicrosoft.CognitiveServices.Speech.core.so", "build/monoandroid/libs/x86/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "build/monoandroid/libs/x86/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "build/monoandroid/libs/x86/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "build/monoandroid/libs/x86/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "build/monoandroid/libs/x86_64/libMicrosoft.CognitiveServices.Speech.core.so", "build/monoandroid/libs/x86_64/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "build/monoandroid/libs/x86_64/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "build/monoandroid/libs/x86_64/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "build/monoandroid/libs/x86_64/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "build/native/ARM/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/ARM64/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/Microsoft.CognitiveServices.Speech.targets", "build/native/Win32/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/include/c_api/CMakeLists.txt", "build/native/include/c_api/azac_api_c_common.h", "build/native/include/c_api/azac_api_c_diagnostics.h", "build/native/include/c_api/azac_api_c_error.h", "build/native/include/c_api/azac_api_c_pal.h", "build/native/include/c_api/azac_debug.h", "build/native/include/c_api/azac_error.h", "build/native/include/c_api/speechapi_c.h", "build/native/include/c_api/speechapi_c_audio_config.h", "build/native/include/c_api/speechapi_c_audio_processing_options.h", "build/native/include/c_api/speechapi_c_audio_stream.h", "build/native/include/c_api/speechapi_c_audio_stream_format.h", "build/native/include/c_api/speechapi_c_auto_detect_source_lang_config.h", "build/native/include/c_api/speechapi_c_common.h", "build/native/include/c_api/speechapi_c_connection.h", "build/native/include/c_api/speechapi_c_conversation.h", "build/native/include/c_api/speechapi_c_conversation_transcription_result.h", "build/native/include/c_api/speechapi_c_conversation_translator.h", "build/native/include/c_api/speechapi_c_diagnostics.h", "build/native/include/c_api/speechapi_c_dialog_service_config.h", "build/native/include/c_api/speechapi_c_dialog_service_connector.h", "build/native/include/c_api/speechapi_c_embedded_speech_config.h", "build/native/include/c_api/speechapi_c_error.h", "build/native/include/c_api/speechapi_c_ext_audiocompression.h", "build/native/include/c_api/speechapi_c_factory.h", "build/native/include/c_api/speechapi_c_grammar.h", "build/native/include/c_api/speechapi_c_hybrid_speech_config.h", "build/native/include/c_api/speechapi_c_intent_recognizer.h", "build/native/include/c_api/speechapi_c_intent_result.h", "build/native/include/c_api/speechapi_c_intent_trigger.h", "build/native/include/c_api/speechapi_c_json.h", "build/native/include/c_api/speechapi_c_keyword_recognition_model.h", "build/native/include/c_api/speechapi_c_language_understanding_model.h", "build/native/include/c_api/speechapi_c_meeting.h", "build/native/include/c_api/speechapi_c_meeting_transcription_result.h", "build/native/include/c_api/speechapi_c_operations.h", "build/native/include/c_api/speechapi_c_participant.h", "build/native/include/c_api/speechapi_c_pattern_matching_model.h", "build/native/include/c_api/speechapi_c_pronunciation_assessment_config.h", "build/native/include/c_api/speechapi_c_property_bag.h", "build/native/include/c_api/speechapi_c_recognizer.h", "build/native/include/c_api/speechapi_c_result.h", "build/native/include/c_api/speechapi_c_session.h", "build/native/include/c_api/speechapi_c_source_lang_config.h", "build/native/include/c_api/speechapi_c_speaker_recognition.h", "build/native/include/c_api/speechapi_c_speech_config.h", "build/native/include/c_api/speechapi_c_speech_recognition_model.h", "build/native/include/c_api/speechapi_c_speech_translation_config.h", "build/native/include/c_api/speechapi_c_speech_translation_model.h", "build/native/include/c_api/speechapi_c_synthesizer.h", "build/native/include/c_api/speechapi_c_translation_recognizer.h", "build/native/include/c_api/speechapi_c_translation_result.h", "build/native/include/c_api/speechapi_c_user.h", "build/native/include/c_api/spxdebug.h", "build/native/include/c_api/spxerror.h", "build/native/include/cxx_api/CMakeLists.txt", "build/native/include/cxx_api/azac_api_cxx_common.h", "build/native/include/cxx_api/speechapi_cxx.h", "build/native/include/cxx_api/speechapi_cxx_audio_config.h", "build/native/include/cxx_api/speechapi_cxx_audio_data_stream.h", "build/native/include/cxx_api/speechapi_cxx_audio_processing_options.h", "build/native/include/cxx_api/speechapi_cxx_audio_stream.h", "build/native/include/cxx_api/speechapi_cxx_audio_stream_format.h", "build/native/include/cxx_api/speechapi_cxx_auto_detect_source_lang_config.h", "build/native/include/cxx_api/speechapi_cxx_auto_detect_source_lang_result.h", "build/native/include/cxx_api/speechapi_cxx_class_language_model.h", "build/native/include/cxx_api/speechapi_cxx_common.h", "build/native/include/cxx_api/speechapi_cxx_connection.h", "build/native/include/cxx_api/speechapi_cxx_connection_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_connection_message.h", "build/native/include/cxx_api/speechapi_cxx_connection_message_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_conversation.h", "build/native/include/cxx_api/speechapi_cxx_conversation_transcriber.h", "build/native/include/cxx_api/speechapi_cxx_conversation_transcription_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_conversation_transcription_result.h", "build/native/include/cxx_api/speechapi_cxx_conversation_translator.h", "build/native/include/cxx_api/speechapi_cxx_conversation_translator_events.h", "build/native/include/cxx_api/speechapi_cxx_conversational_language_understanding_model.h", "build/native/include/cxx_api/speechapi_cxx_dialog_service_config.h", "build/native/include/cxx_api/speechapi_cxx_dialog_service_connector.h", "build/native/include/cxx_api/speechapi_cxx_dialog_service_connector_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_embedded_speech_config.h", "build/native/include/cxx_api/speechapi_cxx_enums.h", "build/native/include/cxx_api/speechapi_cxx_event_logger.h", "build/native/include/cxx_api/speechapi_cxx_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_eventsignal.h", "build/native/include/cxx_api/speechapi_cxx_eventsignalbase.h", "build/native/include/cxx_api/speechapi_cxx_file_logger.h", "build/native/include/cxx_api/speechapi_cxx_grammar.h", "build/native/include/cxx_api/speechapi_cxx_grammar_list.h", "build/native/include/cxx_api/speechapi_cxx_grammar_phrase.h", "build/native/include/cxx_api/speechapi_cxx_hybrid_speech_config.h", "build/native/include/cxx_api/speechapi_cxx_intent_recognition_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_intent_recognition_result.h", "build/native/include/cxx_api/speechapi_cxx_intent_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_intent_trigger.h", "build/native/include/cxx_api/speechapi_cxx_keyword_recognition_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_keyword_recognition_model.h", "build/native/include/cxx_api/speechapi_cxx_keyword_recognition_result.h", "build/native/include/cxx_api/speechapi_cxx_keyword_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_language_understanding_model.h", "build/native/include/cxx_api/speechapi_cxx_log_level.h", "build/native/include/cxx_api/speechapi_cxx_meeting.h", "build/native/include/cxx_api/speechapi_cxx_meeting_transcriber.h", "build/native/include/cxx_api/speechapi_cxx_meeting_transcription_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_meeting_transcription_result.h", "build/native/include/cxx_api/speechapi_cxx_memory_logger.h", "build/native/include/cxx_api/speechapi_cxx_participant.h", "build/native/include/cxx_api/speechapi_cxx_pattern_matching_entity.h", "build/native/include/cxx_api/speechapi_cxx_pattern_matching_intent.h", "build/native/include/cxx_api/speechapi_cxx_pattern_matching_model.h", "build/native/include/cxx_api/speechapi_cxx_phrase_list_grammar.h", "build/native/include/cxx_api/speechapi_cxx_pronunciation_assessment_config.h", "build/native/include/cxx_api/speechapi_cxx_pronunciation_assessment_result.h", "build/native/include/cxx_api/speechapi_cxx_properties.h", "build/native/include/cxx_api/speechapi_cxx_recognition_async_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_recognition_base_async_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_recognition_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_recognition_result.h", "build/native/include/cxx_api/speechapi_cxx_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_session.h", "build/native/include/cxx_api/speechapi_cxx_session_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_smart_handle.h", "build/native/include/cxx_api/speechapi_cxx_source_lang_config.h", "build/native/include/cxx_api/speechapi_cxx_source_language_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_speaker_identification_model.h", "build/native/include/cxx_api/speechapi_cxx_speaker_recognition_result.h", "build/native/include/cxx_api/speechapi_cxx_speaker_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_speaker_verification_model.h", "build/native/include/cxx_api/speechapi_cxx_speech_config.h", "build/native/include/cxx_api/speechapi_cxx_speech_recognition_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_speech_recognition_model.h", "build/native/include/cxx_api/speechapi_cxx_speech_recognition_result.h", "build/native/include/cxx_api/speechapi_cxx_speech_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_bookmark_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_result.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_viseme_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesis_word_boundary_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_speech_synthesizer.h", "build/native/include/cxx_api/speechapi_cxx_speech_translation_config.h", "build/native/include/cxx_api/speechapi_cxx_speech_translation_model.h", "build/native/include/cxx_api/speechapi_cxx_string_helpers.h", "build/native/include/cxx_api/speechapi_cxx_synthesis_voices_result.h", "build/native/include/cxx_api/speechapi_cxx_translation_eventargs.h", "build/native/include/cxx_api/speechapi_cxx_translation_recognizer.h", "build/native/include/cxx_api/speechapi_cxx_translation_result.h", "build/native/include/cxx_api/speechapi_cxx_user.h", "build/native/include/cxx_api/speechapi_cxx_utils.h", "build/native/include/cxx_api/speechapi_cxx_voice_info.h", "build/native/include/cxx_api/speechapi_cxx_voice_profile.h", "build/native/include/cxx_api/speechapi_cxx_voice_profile_client.h", "build/native/include/cxx_api/speechapi_cxx_voice_profile_enrollment_result.h", "build/native/include/cxx_api/speechapi_cxx_voice_profile_phrase_result.h", "build/native/include/cxx_api/speechapi_cxx_voice_profile_result.h", "build/native/uap/ARM/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/uap/ARM64/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/uap/Win32/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/uap/x64/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/native/x64/Release/Microsoft.CognitiveServices.Speech.core.lib", "build/net462/Microsoft.CognitiveServices.Speech.targets", "build/net6.0-android/Microsoft.CognitiveServices.Speech.targets", "build/net6.0-ios/Microsoft.CognitiveServices.Speech.targets", "lib/Xamarin.iOS/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/Xamarin.iOS/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/monoandroid/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/monoandroid/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/net462/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/net462/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/net6.0-android/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/net6.0-android/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/net6.0-ios/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/net6.0-ios/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/net6.0-maccatalyst/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/net6.0-maccatalyst/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "lib/uap10.0/Microsoft.CognitiveServices.Speech.csharp.dll", "lib/uap10.0/Microsoft.CognitiveServices.Speech.csharp.xml", "microsoft.cognitiveservices.speech.1.34.1.nupkg.sha512", "microsoft.cognitiveservices.speech.nuspec", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so", "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/ios-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a", "runtimes/iossimulator-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a", "runtimes/iossimulator-x64/native/libMicrosoft.CognitiveServices.Speech.core.a", "runtimes/linux-arm/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "runtimes/linux-arm/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/linux-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "runtimes/linux-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/linux-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "runtimes/linux-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.core.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so", "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so", "runtimes/maccatalyst-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a", "runtimes/maccatalyst-x64/native/libMicrosoft.CognitiveServices.Speech.core.a", "runtimes/osx-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "runtimes/osx-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.core.dylib", "runtimes/osx-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll", "runtimes/osx-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.xml", "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.core.dylib", "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-arm/nativeassets/uap/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-arm/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-arm/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-arm/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-arm/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-arm64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.codec.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-x64/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.codec.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.lu.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.core.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll", "runtimes/win-x86/nativeassets/uap/Microsoft.CognitiveServices.Speech.extension.lu.dll"]}, "Microsoft.Extensions.AI.Abstractions/9.3.0-preview.1.25114.11": {"sha512": "Bh5RqAGzOUTSnx6vLrQqVH5LJXSPYmMF8/worfSmKiJ18blVQRHLqvpcc/JhSdFf/3YTOk2PPpWSX1PK+cbiPQ==", "type": "package", "path": "microsoft.extensions.ai.abstractions/9.3.0-preview.1.25114.11", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "lib/net462/Microsoft.Extensions.AI.Abstractions.dll", "lib/net462/Microsoft.Extensions.AI.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.AI.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.AI.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.AI.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.AI.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.AI.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.AI.Abstractions.xml", "microsoft.extensions.ai.abstractions.9.3.0-preview.1.25114.11.nupkg.sha512", "microsoft.extensions.ai.abstractions.nuspec"]}, "Microsoft.Extensions.Configuration/8.0.0": {"sha512": "0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "type": "package", "path": "microsoft.extensions.configuration/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net6.0/Microsoft.Extensions.Configuration.dll", "lib/net6.0/Microsoft.Extensions.Configuration.xml", "lib/net7.0/Microsoft.Extensions.Configuration.dll", "lib/net7.0/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"sha512": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"sha512": "mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "type": "package", "path": "microsoft.extensions.configuration.binder/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"sha512": "NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "type": "package", "path": "microsoft.extensions.configuration.commandline/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.CommandLine.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.CommandLine.targets", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net7.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net7.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"sha512": "plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net7.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net7.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"sha512": "McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"sha512": "C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "type": "package", "path": "microsoft.extensions.configuration.json/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"sha512": "ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net7.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net7.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"sha512": "V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"sha512": "3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics/8.0.0": {"sha512": "3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "type": "package", "path": "microsoft.extensions.diagnostics/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.targets", "lib/net462/Microsoft.Extensions.Diagnostics.dll", "lib/net462/Microsoft.Extensions.Diagnostics.xml", "lib/net6.0/Microsoft.Extensions.Diagnostics.dll", "lib/net6.0/Microsoft.Extensions.Diagnostics.xml", "lib/net7.0/Microsoft.Extensions.Diagnostics.dll", "lib/net7.0/Microsoft.Extensions.Diagnostics.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.xml", "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512", "microsoft.extensions.diagnostics.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"sha512": "JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"sha512": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"sha512": "UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"sha512": "OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting/8.0.0": {"sha512": "ItYHpdqVp5/oFLT5QqbopnkKlyFG9EW/9nhM6/yfObeKt6Su0wkBio6AizgRHGNwhJuAtlE5VIjow5JOTrip6w==", "type": "package", "path": "microsoft.extensions.hosting/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.targets", "lib/net462/Microsoft.Extensions.Hosting.dll", "lib/net462/Microsoft.Extensions.Hosting.xml", "lib/net6.0/Microsoft.Extensions.Hosting.dll", "lib/net6.0/Microsoft.Extensions.Hosting.xml", "lib/net7.0/Microsoft.Extensions.Hosting.dll", "lib/net7.0/Microsoft.Extensions.Hosting.xml", "lib/net8.0/Microsoft.Extensions.Hosting.dll", "lib/net8.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.xml", "microsoft.extensions.hosting.8.0.0.nupkg.sha512", "microsoft.extensions.hosting.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"sha512": "AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Http/8.0.0": {"sha512": "cWz4caHwvx0emoYe7NkHPxII/KkTI8R/LC9qdqJqnKv2poTJ4e2qqPGQqvRoQ5kaSA4FU5IV3qFAuLuOhoqULQ==", "type": "package", "path": "microsoft.extensions.http/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Http.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Http.targets", "lib/net462/Microsoft.Extensions.Http.dll", "lib/net462/Microsoft.Extensions.Http.xml", "lib/net6.0/Microsoft.Extensions.Http.dll", "lib/net6.0/Microsoft.Extensions.Http.xml", "lib/net7.0/Microsoft.Extensions.Http.dll", "lib/net7.0/Microsoft.Extensions.Http.xml", "lib/net8.0/Microsoft.Extensions.Http.dll", "lib/net8.0/Microsoft.Extensions.Http.xml", "lib/netstandard2.0/Microsoft.Extensions.Http.dll", "lib/netstandard2.0/Microsoft.Extensions.Http.xml", "microsoft.extensions.http.8.0.0.nupkg.sha512", "microsoft.extensions.http.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/8.0.0": {"sha512": "tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "type": "package", "path": "microsoft.extensions.logging/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net6.0/Microsoft.Extensions.Logging.dll", "lib/net6.0/Microsoft.Extensions.Logging.xml", "lib/net7.0/Microsoft.Extensions.Logging.dll", "lib/net7.0/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.8.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/8.0.3": {"sha512": "dL0QGToTxggRLMYY4ZYX5AMwBb+byQBd/5dMiZE07Nv73o6I5Are3C7eQTh7K2+A4ct0PVISSr7TZANbiNb2yQ==", "type": "package", "path": "microsoft.extensions.logging.abstractions/8.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.8.0.3.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"sha512": "ixXXV0G/12g6MXK65TLngYN9V5hQQRuV+fZi882WIoVJT7h5JvoYoxTEwCgdqwLjSneqh1O+66gM8sMr9z/rsQ==", "type": "package", "path": "microsoft.extensions.logging.configuration/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Configuration.targets", "lib/net462/Microsoft.Extensions.Logging.Configuration.dll", "lib/net462/Microsoft.Extensions.Logging.Configuration.xml", "lib/net6.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net6.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net7.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net7.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.xml", "microsoft.extensions.logging.configuration.8.0.0.nupkg.sha512", "microsoft.extensions.logging.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Console/8.0.0": {"sha512": "e+48o7DztoYog+PY430lPxrM4mm3PbA6qucvQtUDDwVo4MO+ejMw7YGc/o2rnxbxj4isPxdfKFzTxvXMwAz83A==", "type": "package", "path": "microsoft.extensions.logging.console/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Console.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Console.targets", "lib/net462/Microsoft.Extensions.Logging.Console.dll", "lib/net462/Microsoft.Extensions.Logging.Console.xml", "lib/net6.0/Microsoft.Extensions.Logging.Console.dll", "lib/net6.0/Microsoft.Extensions.Logging.Console.xml", "lib/net7.0/Microsoft.Extensions.Logging.Console.dll", "lib/net7.0/Microsoft.Extensions.Logging.Console.xml", "lib/net8.0/Microsoft.Extensions.Logging.Console.dll", "lib/net8.0/Microsoft.Extensions.Logging.Console.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.xml", "microsoft.extensions.logging.console.8.0.0.nupkg.sha512", "microsoft.extensions.logging.console.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"sha512": "dt0x21qBdudHLW/bjMJpkixv858RRr8eSomgVbU8qljOyfrfDGi1JQvpF9w8S7ziRPtRKisuWaOwFxJM82GxeA==", "type": "package", "path": "microsoft.extensions.logging.debug/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Debug.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Debug.targets", "lib/net462/Microsoft.Extensions.Logging.Debug.dll", "lib/net462/Microsoft.Extensions.Logging.Debug.xml", "lib/net6.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net6.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net7.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net7.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net8.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net8.0/Microsoft.Extensions.Logging.Debug.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.xml", "microsoft.extensions.logging.debug.8.0.0.nupkg.sha512", "microsoft.extensions.logging.debug.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"sha512": "3X9D3sl7EmOu7vQp5MJrmIJBl5XSdOhZPYXUeFfYa6Nnm9+tok8x3t3IVPLhm7UJtPOU61ohFchw8rNm9tIYOQ==", "type": "package", "path": "microsoft.extensions.logging.eventlog/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventLog.targets", "lib/net462/Microsoft.Extensions.Logging.EventLog.dll", "lib/net462/Microsoft.Extensions.Logging.EventLog.xml", "lib/net6.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net6.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/net7.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net7.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.xml", "microsoft.extensions.logging.eventlog.8.0.0.nupkg.sha512", "microsoft.extensions.logging.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"sha512": "oKcPMrw+luz2DUAKhwFXrmFikZWnyc8l2RKoQwqU3KIZZjcfoJE0zRHAnqATfhRZhtcbjl/QkiY2Xjxp0xu+6w==", "type": "package", "path": "microsoft.extensions.logging.eventsource/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventSource.targets", "lib/net462/Microsoft.Extensions.Logging.EventSource.dll", "lib/net462/Microsoft.Extensions.Logging.EventSource.xml", "lib/net6.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net6.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/net7.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net7.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.xml", "microsoft.extensions.logging.eventsource.8.0.0.nupkg.sha512", "microsoft.extensions.logging.eventsource.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/8.0.0": {"sha512": "JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "type": "package", "path": "microsoft.extensions.options/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net6.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.8.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"sha512": "0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/8.0.0": {"sha512": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "type": "package", "path": "microsoft.extensions.primitives/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.8.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.NETCore.Platforms/3.1.0": {"sha512": "z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "type": "package", "path": "microsoft.netcore.platforms/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.3.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Toolkit.Mvvm/7.1.2": {"sha512": "tUQn6ar9oP7kmYaZ6pWabxpQaqw95Bsf+GG7prEL0y5MV5F60z76lI/Ppps9zcTqwd7e5CrdZBcdfor9WuvohA==", "type": "package", "path": "microsoft.toolkit.mvvm/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/cs/Microsoft.Toolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/cs/Microsoft.Toolkit.Mvvm.SourceGenerators.pdb", "analyzers/dotnet/cs/Microsoft.Toolkit.Mvvm.SourceGenerators.xml", "lib/net5.0/Microsoft.Toolkit.Mvvm.dll", "lib/net5.0/Microsoft.Toolkit.Mvvm.pdb", "lib/net5.0/Microsoft.Toolkit.Mvvm.xml", "lib/netstandard2.0/Microsoft.Toolkit.Mvvm.dll", "lib/netstandard2.0/Microsoft.Toolkit.Mvvm.pdb", "lib/netstandard2.0/Microsoft.Toolkit.Mvvm.xml", "lib/netstandard2.1/Microsoft.Toolkit.Mvvm.dll", "lib/netstandard2.1/Microsoft.Toolkit.Mvvm.pdb", "lib/netstandard2.1/Microsoft.Toolkit.Mvvm.xml", "microsoft.toolkit.mvvm.7.1.2.nupkg.sha512", "microsoft.toolkit.mvvm.nuspec"]}, "Microsoft.Win32.Registry/4.7.0": {"sha512": "KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "type": "package", "path": "microsoft.win32.registry/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.4.7.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/net472/Microsoft.Win32.Registry.dll", "ref/net472/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "NAudio/2.2.1": {"sha512": "c0DzwiyyklM0TP39Y7RObwO3QkWecgM6H60ikiEnsV/aEAJPbj5MFCLaD8BSfKuZe0HGuh9GRGWWlJmSxDc9MA==", "type": "package", "path": "naudio/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/NAudio.dll", "lib/net472/NAudio.xml", "lib/net6.0-windows7.0/NAudio.dll", "lib/net6.0-windows7.0/NAudio.xml", "lib/net6.0/NAudio.dll", "lib/net6.0/NAudio.xml", "lib/netcoreapp3.1/NAudio.dll", "lib/netcoreapp3.1/NAudio.xml", "license.txt", "naudio-icon.png", "naudio.2.2.1.nupkg.sha512", "naudio.nuspec"]}, "NAudio.Asio/2.2.1": {"sha512": "hQglyOT5iT3XuGpBP8ZG0+aoqwRfidHjTNehpoWwX0g6KJEgtH2VaqM2nuJ2mheKZa/IBqB4YQTZVvrIapzfOA==", "type": "package", "path": "naudio.asio/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.Asio.dll", "lib/netstandard2.0/NAudio.Asio.xml", "naudio-icon.png", "naudio.asio.2.2.1.nupkg.sha512", "naudio.asio.nuspec"]}, "NAudio.Core/2.2.1": {"sha512": "GgkdP6K/7FqXFo7uHvoqGZTJvW4z8g2IffhOO4JHaLzKCdDOUEzVKtveoZkCuUX8eV2HAINqi7VFqlFndrnz/g==", "type": "package", "path": "naudio.core/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.Core.dll", "lib/netstandard2.0/NAudio.Core.xml", "naudio-icon.png", "naudio.core.2.2.1.nupkg.sha512", "naudio.core.nuspec"]}, "NAudio.Midi/2.2.1": {"sha512": "6r23ylGo5aeP02WFXsPquz0T0hFJWyh+7t++tz19tc3Kr38NHm+Z9j+FiAv+xkH8tZqXJqus9Q8p6u7bidIgbw==", "type": "package", "path": "naudio.midi/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.Midi.dll", "lib/netstandard2.0/NAudio.Midi.xml", "naudio-icon.png", "naudio.midi.2.2.1.nupkg.sha512", "naudio.midi.nuspec"]}, "NAudio.Wasapi/2.2.1": {"sha512": "lFfXoqacZZe0WqNChJgGYI+XV/n/61LzPHT3C1CJp4khoxeo2sziyX5wzNYWeCMNbsWxFvT3b3iXeY1UYjBhZw==", "type": "package", "path": "naudio.wasapi/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.Wasapi.dll", "lib/netstandard2.0/NAudio.Wasapi.xml", "lib/uap10.0.18362/NAudio.Wasapi.dll", "lib/uap10.0.18362/NAudio.Wasapi.pri", "lib/uap10.0.18362/NAudio.Wasapi.xml", "naudio-icon.png", "naudio.wasapi.2.2.1.nupkg.sha512", "naudio.wasapi.nuspec"]}, "NAudio.WinForms/2.2.1": {"sha512": "DlDkewY1myY0A+3NrYRJD+MZhZV0yy1mNF6dckB27IQ9XCs/My5Ip8BZcoSHOsaPSe2GAjvoaDnk6N9w8xTv7w==", "type": "package", "path": "naudio.winforms/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/NAudio.WinForms.dll", "lib/net472/NAudio.WinForms.xml", "lib/netcoreapp3.1/NAudio.WinForms.dll", "lib/netcoreapp3.1/NAudio.WinForms.xml", "naudio-icon.png", "naudio.winforms.2.2.1.nupkg.sha512", "naudio.winforms.nuspec"]}, "NAudio.WinMM/2.2.1": {"sha512": "xFHRFwH4x6aq3IxRbewvO33ugJRvZFEOfO62i7uQJRUNW2cnu6BeBTHUS0JD5KBucZbHZaYqxQG8dwZ47ezQuQ==", "type": "package", "path": "naudio.winmm/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/NAudio.WinMM.dll", "lib/netstandard2.0/NAudio.WinMM.xml", "naudio-icon.png", "naudio.winmm.2.2.1.nupkg.sha512", "naudio.winmm.nuspec"]}, "OpenAI/2.2.0": {"sha512": "MSFrQXZsgdwcJq3b8159oA0sYRJk0Io5Rg+MhbNs1NFIhwQ7IlSP2jao7V0NqBqCRfVhvoNxPZeVOqgbOiZ/Nw==", "type": "package", "path": "openai/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "OpenAI.png", "README.md", "lib/net6.0/OpenAI.dll", "lib/net6.0/OpenAI.xml", "lib/net8.0/OpenAI.dll", "lib/net8.0/OpenAI.xml", "lib/netstandard2.0/OpenAI.dll", "lib/netstandard2.0/OpenAI.xml", "openai.2.2.0.nupkg.sha512", "openai.nuspec"]}, "Refit/7.2.22": {"sha512": "xzpjDvWTKaJkhRejrHI6E2WXs2CaJYWZTMICq5Jha4wlSVvR3dd9n3M4p3X7BQ6uw0LJSFiHlTYwzjQIbJdArg==", "type": "package", "path": "refit/7.2.22", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE/LICENSE", "README.md", "analyzers/dotnet/roslyn3.8/cs/InterfaceStubGeneratorV1.dll", "analyzers/dotnet/roslyn4.0/cs/InterfaceStubGeneratorV2.dll", "build/netstandard2.0/refit.targets", "buildTransitive/netstandard2.0/refit.props", "buildTransitive/netstandard2.0/refit.targets", "lib/net462/Refit.dll", "lib/net462/Refit.xml", "lib/net6.0/Refit.dll", "lib/net6.0/Refit.xml", "lib/net8.0/Refit.dll", "lib/net8.0/Refit.xml", "lib/netstandard2.0/Refit.dll", "lib/netstandard2.0/Refit.xml", "refit.7.2.22.nupkg.sha512", "refit.nuspec", "refit_logo.png"]}, "Refit.HttpClientFactory/7.2.22": {"sha512": "X2Za44VsDTqOB5wNvLrcOh/SE7QAc4d6fdwasbo6ll4yyTVEsdylOBpdBm2/cTw1Hqr55KIBR3g8H+e4VOmBDQ==", "type": "package", "path": "refit.httpclientfactory/7.2.22", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE/LICENSE", "README.md", "lib/net462/Refit.HttpClientFactory.dll", "lib/net462/Refit.HttpClientFactory.xml", "lib/net6.0/Refit.HttpClientFactory.dll", "lib/net6.0/Refit.HttpClientFactory.xml", "lib/net8.0/Refit.HttpClientFactory.dll", "lib/net8.0/Refit.HttpClientFactory.xml", "lib/netstandard2.0/Refit.HttpClientFactory.dll", "lib/netstandard2.0/Refit.HttpClientFactory.xml", "refit.httpclientfactory.7.2.22.nupkg.sha512", "refit.httpclientfactory.nuspec", "refit_logo.png"]}, "System.ClientModel/1.4.2": {"sha512": "goGitN7trB9hoQ01dIpxaSYcruI+lGt/xq471AUv8irFvsIX+4HCqk1pDT/4ZPTLmU6ZUuNzhCb4MJAIwG7+Uw==", "type": "package", "path": "system.clientmodel/1.4.2", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "analyzers/dotnet/cs/System.ClientModel.SourceGeneration.dll", "lib/net6.0/System.ClientModel.dll", "lib/net6.0/System.ClientModel.xml", "lib/net8.0/System.ClientModel.dll", "lib/net8.0/System.ClientModel.xml", "lib/netstandard2.0/System.ClientModel.dll", "lib/netstandard2.0/System.ClientModel.xml", "system.clientmodel.1.4.2.nupkg.sha512", "system.clientmodel.nuspec"]}, "System.Diagnostics.DiagnosticSource/8.0.0": {"sha512": "c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/net7.0/System.Diagnostics.DiagnosticSource.dll", "lib/net7.0/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/8.0.0": {"sha512": "fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A==", "type": "package", "path": "system.diagnostics.eventlog/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/net7.0/System.Diagnostics.EventLog.dll", "lib/net7.0/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.8.0.0.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory.Data/6.0.1": {"sha512": "yliDgLh9S9Mcy5hBIdZmX6yphYIW3NH+3HN1kV1m7V1e0s7LNTw/tHNjJP4U9nSMEgl3w1TzYv/KA1Tg9NYy6w==", "type": "package", "path": "system.memory.data/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Memory.Data.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Memory.Data.dll", "lib/net461/System.Memory.Data.xml", "lib/net6.0/System.Memory.Data.dll", "lib/net6.0/System.Memory.Data.xml", "lib/netstandard2.0/System.Memory.Data.dll", "lib/netstandard2.0/System.Memory.Data.xml", "system.memory.data.6.0.1.nupkg.sha512", "system.memory.data.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.AccessControl/4.7.0": {"sha512": "JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "type": "package", "path": "system.security.accesscontrol/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.4.7.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/4.7.0": {"sha512": "ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "type": "package", "path": "system.security.principal.windows/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.4.7.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Json/8.0.5": {"sha512": "0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "type": "package", "path": "system.text.json/8.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.5.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["Anthropic.SDK >= 4.7.1", "Markdig.Wpf >= *******", "Microsoft.CognitiveServices.Speech >= 1.34.1", "Microsoft.Extensions.DependencyInjection >= 8.0.0", "Microsoft.Extensions.Hosting >= 8.0.0", "Microsoft.Extensions.Http >= 8.0.0", "Microsoft.Toolkit.Mvvm >= 7.1.2", "NAudio >= 2.2.1", "NAudio.Wasapi >= 2.2.1", "OpenAI >= 2.2.0", "Refit >= 7.2.22", "Refit.HttpClientFactory >= 7.2.22", "System.Text.Json >= 8.0.5"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\code\\interview_copilot\\InterviewCopilot.csproj", "projectName": "InterviewCop<PERSON>t", "projectPath": "C:\\code\\interview_copilot\\InterviewCopilot.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\code\\interview_copilot\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Anthropic.SDK": {"target": "Package", "version": "[4.7.1, )"}, "Markdig.Wpf": {"target": "Package", "version": "[*******, )"}, "Microsoft.CognitiveServices.Speech": {"target": "Package", "version": "[1.34.1, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Toolkit.Mvvm": {"target": "Package", "version": "[7.1.2, )"}, "NAudio": {"target": "Package", "version": "[2.2.1, )"}, "NAudio.Wasapi": {"target": "Package", "version": "[2.2.1, )"}, "OpenAI": {"target": "Package", "version": "[2.2.0, )"}, "Refit": {"target": "Package", "version": "[7.2.22, )"}, "Refit.HttpClientFactory": {"target": "Package", "version": "[7.2.22, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}}